# 🔬 Gelişmiş DOI Makale Özet Sistemi

Bu proje, DOI numarası verilen tıbbi makalelerin otomatik olarak özetlenmesi ve profesyonel görsel infografik oluşturulması için geliştirilmiştir.

## ✨ Gelişmiş Özellikler

- 📄 **Çoklu Kaynak Desteği**: DOI ile birden fazla API'den veri çekme
  - Primary: @uwdata/citation-query
  - Crossref API
  - Semantic Scholar API
  - PubMed API (tıbbi makaleler için)
- 🧠 **Akıllı AI Analizi**: Sınırlı verilerle bile anlamlı özetler oluşturma
- 📊 **Gelişmiş SVG Görselleştirme**: Veri kalitesi göstergeleri ile profesyonel infografikler
- 🏥 **Tıbbi Makale Odaklı**: Metodoloji, bulgular, sonuç ve istatistiksel analiz özetleri
- ⚠️ **Veri Kalitesi Değerlendirmesi**: FULL/PARTIAL/LIMITED kalite göstergeleri
- 🔍 **<PERSON>k<PERSON><PERSON><PERSON>**: Eksik bilgiler için makul tahminler ve açık sınırlamalar

## Kurulum

1. Bağımlılıkları yükleyin:
```bash
npm install
```

2. Ortam değişkenlerini ayarlayın (`.env` dosyası oluşturun):
```
OPENAI_API_KEY=your_openai_api_key_here
```

## Kullanım

### 1. Web Arayüzü (Önerilen)

```bash
# API Proxy server'ı başlatın (CORS sorunlarını çözer)
node api-proxy.js

# Başka bir terminalde web server'ı başlatın
python -m http.server 8000 --directory public
```

Tarayıcınızda `http://localhost:8000` adresine gidin ve DOI numaranızı girin.

**Not**: Web sitesi önce API Proxy'yi (`localhost:3001`) dener, başarısız olursa Mastra API'yi (`localhost:4111`) dener.

### 2. Gelişmiş Test (Önerilen)

```bash
node final-test.js
```

Bu komut gelişmiş SVG ile tam entegrasyon testi yapar ve profesyonel infografik oluşturur.

### 3. Çoklu Kaynak Test

```bash
node enhanced-test.js
```

Bu komut birden fazla API kaynağından veri çeker ve kapsamlı analiz yapar.

### 4. Basit Testler

```bash
# SVG oluşturma testi
node svg-test.js

# DOI lookup testi
node simple-test.js
```

### 5. API Proxy Test

```bash
node test-proxy.js
```

Bu komut API Proxy'nin çalışıp çalışmadığını test eder.

### 6. Mastra Dev Server (AI Workflow)

```bash
npm run dev
```

Bu komut Mastra geliştirme sunucusunu başlatır (`http://localhost:4111`) ve AI agent'ları test edebilirsiniz.

## Desteklenen DOI Formatları

- `10.1016/j.kint.2021.02.040`
- `https://doi.org/10.1016/j.kint.2021.02.040`
- `https://dx.doi.org/10.1016/j.kint.2021.02.040`

## 📊 Gelişmiş Çıktı Formatı

Sistem aşağıdaki bilgileri çıkarır ve akıllıca görselleştirir:

### 📋 Analiz Edilen Bilgiler:
- **Makale Metadata**: Başlık, yazarlar, dergi, yayın yılı, atıf sayısı
- **Özet Analizi**: Mevcut özet veya "özet mevcut değil" uyarısı
- **Metodoloji**: Çalışma tasarımı ve yöntemler (tahmin edilmiş)
- **Ana Bulgular**: Önemli sonuçlar (mevcut verilerle çıkarılmış)
- **İstatistiksel Analiz**: Kullanılan yöntemler (tahmin edilmiş)
- **Sonuç**: Araştırmanın genel sonuçları
- **Veri Kalitesi**: FULL/PARTIAL/LIMITED değerlendirmesi
- **Sınırlamalar**: Analiz kısıtları ve eksik bilgiler

### 🎨 Gelişmiş Görsel Çıktı:
- **Veri Kalitesi Göstergesi**: Renkli kalite durumu (Yeşil/Turuncu/Kırmızı)
- **Akıllı Uyarılar**: Eksik veri durumunda bilgilendirme
- **Profesyonel Tasarım**: Gradyan arka planlar ve modern görünüm
- **Responsive Layout**: 1000x750 piksel optimum boyut
- **Atıf ve PMID Bilgileri**: Mevcut olduğunda ek metadata
- **Anahtar Kelimeler**: Makale konularını gösteren etiketler

## Teknolojiler

- **Mastra**: Workflow ve agent yönetimi
- **OpenAI GPT-4**: Makale analizi ve özetleme
- **@uwdata/citation-query**: DOI ile makale verisi çekme
- **SVG.js**: Dinamik SVG oluşturma
- **TypeScript**: Tip güvenliği

## Örnek Kullanım

### Web Arayüzü
1. `http://localhost:8000` adresine gidin
2. DOI numarasını girin (örn: `10.1016/j.kint.2021.02.040`)
3. "Analiz Et" butonuna tıklayın
4. Oluşturulan SVG infografiği indirin

### Programatik Kullanım
```javascript
// DOI lookup testi
import { lookupDOI } from '@uwdata/citation-query';
const data = await lookupDOI('10.1016/j.kint.2021.02.040');

// SVG oluşturma
import { createMedicalSummarySVG } from './src/mastra/agents/article-summary-agent.js';
const svg = createMedicalSummarySVG(data);
```

## Katkıda Bulunma

1. Bu repository'yi fork edin
2. Feature branch oluşturun (`git checkout -b feature/yeni-ozellik`)
3. Değişikliklerinizi commit edin (`git commit -am 'Yeni özellik eklendi'`)
4. Branch'inizi push edin (`git push origin feature/yeni-ozellik`)
5. Pull Request oluşturun

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
