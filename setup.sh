#!/bin/bash

echo "🚀 Academic Article Analyzer Setup"
echo "=================================="

# Ruby Backend Setup
echo ""
echo "📦 Setting up Ruby Backend..."
cd backend

# Check if Ruby is installed
if ! command -v ruby &> /dev/null; then
    echo "❌ Ruby is not installed. Please install Ruby first."
    exit 1
fi

# Check if <PERSON><PERSON><PERSON> is installed
if ! command -v bundle &> /dev/null; then
    echo "📦 Installing Bundler..."
    gem install bundler
fi

# Install Ruby gems
echo "📦 Installing Ruby gems..."
bundle install

cd ..

# Node.js CLI Agent Setup
echo ""
echo "📦 Setting up CLI Agent..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Install Node.js dependencies (if not already installed)
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Next.js Frontend Setup
echo ""
echo "📦 Setting up Next.js Frontend..."
cd frontend

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
npm install

cd ..

# Test CLI Agent
echo ""
echo "🧪 Testing CLI Agent..."
node test-cli-agent.js

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Setup completed successfully!"
    echo ""
    echo "🚀 To start the application:"
    echo "   1. Start Ruby Backend:  cd backend && ruby app.rb"
    echo "   2. Start Next.js Frontend:  cd frontend && npm run dev"
    echo "   3. Open browser:  http://localhost:3000"
    echo ""
    echo "🧪 To test:"
    echo "   - Backend test:  ruby backend/test-backend.rb"
    echo "   - CLI agent test:  node test-cli-agent.js"
else
    echo ""
    echo "❌ Setup failed during CLI agent test"
    echo "Please check the error messages above"
    exit 1
fi
