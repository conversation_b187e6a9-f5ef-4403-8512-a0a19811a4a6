{"name": "abstract-essay", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@mastra/core": "^0.11.1", "@mastra/libsql": "^0.11.2", "@mastra/loggers": "^0.10.4", "@mastra/memory": "^0.11.5", "@svgdotjs/svg.js": "^3.2.4", "@uwdata/citation-query": "^0.0.1", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "cors": "^2.8.5", "express": "^5.1.0", "lucide-react": "^0.528.0", "next": "^15.4.4", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "svgdom": "^0.1.22", "tailwindcss": "^4.1.11", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^24.1.0", "mastra": "^0.10.15", "typescript": "^5.8.3"}}