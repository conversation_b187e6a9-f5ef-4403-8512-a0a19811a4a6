// CLI Agent test
import { spawn } from 'child_process';

async function testCLIAgent() {
  console.log('🧪 CLI Agent test başlıyor...\n');
  
  const testDOI = '10.1007/s44163-022-00022-8';
  console.log(`📄 Test DOI: ${testDOI}`);
  
  return new Promise((resolve, reject) => {
    const child = spawn('node', ['cli-agent.js', testDOI], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      console.log(`\n📊 CLI Agent çıkış kodu: ${code}`);
      
      if (code === 0) {
        try {
          const result = JSON.parse(stdout);
          console.log('✅ JSON parse başarılı');
          console.log(`📖 Başlık: ${result.title}`);
          console.log(`🎯 Alan: ${result.field}`);
          console.log(`🔬 Veri Kalitesi: ${result.dataQuality}`);
          console.log('\n🎉 CLI Agent test başarılı!');
          resolve(result);
        } catch (error) {
          console.error('❌ JSON parse hatası:', error.message);
          console.error('Raw output:', stdout);
          reject(error);
        }
      } else {
        console.error('❌ CLI Agent hatası:');
        console.error('STDERR:', stderr);
        console.error('STDOUT:', stdout);
        reject(new Error(`CLI Agent failed with code ${code}`));
      }
    });
    
    // Timeout
    setTimeout(() => {
      child.kill();
      reject(new Error('CLI Agent timeout'));
    }, 30000);
  });
}

testCLIAgent()
  .then(() => {
    console.log('\n✅ Test tamamlandı!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Test başarısız:', error.message);
    process.exit(1);
  });
