// Basit test - DOI ile makale bilgilerini çekme
import { lookupDOI } from '@uwdata/citation-query';
import fs from 'fs';

async function testDOILookup() {
  try {
    console.log('DOI lookup test başlıyor...');
    
    const testDOI = '10.1016/j.kint.2021.02.040';
    console.log(`Test DOI: ${testDOI}`);
    
    const data = await lookupDOI(testDOI);
    
    console.log('\nMakale Bilgileri:');
    console.log('='.repeat(50));
    console.log('Başlık:', data.title);
    console.log('Yazarlar:', data.author?.map(a => `${a.given || ''} ${a.family || ''}`.trim()).join(', '));
    console.log('Dergi:', data['container-title'] || data.journal);
    console.log('Yıl:', data.issued?.['date-parts']?.[0]?.[0]);
    console.log('Özet:', data.abstract?.slice(0, 200) + '...');
    
    // Tam veriyi dosyaya kaydet
    fs.writeFileSync('doi-data.json', JSON.stringify(data, null, 2));
    console.log('\nTam veri "doi-data.json" dosyasına kaydedildi.');
    
    return data;
    
  } catch (error) {
    console.error('Test hatası:', error);
    throw error;
  }
}

testDOILookup()
  .then(() => {
    console.log('\nDOI lookup test başarıyla tamamlandı!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Test başarısız:', error);
    process.exit(1);
  });
