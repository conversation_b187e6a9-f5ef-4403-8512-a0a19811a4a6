# frozen_string_literal: true

require 'sinatra'
require 'sinatra/cors'
require 'json'
require 'httparty'
require 'open3'
require 'logger'

# CORS ayarları
set :allow_origin, '*'
set :allow_methods, 'GET,HEAD,POST,OPTIONS'
set :allow_headers, 'content-type,if-modified-since'
set :expose_headers, 'location,link'

# Logger ayarları
logger = Logger.new(STDOUT)
logger.level = Logger::INFO

# Unpaywall API ile PDF link alma
class UnpaywallService
  include HTTParty
  base_uri 'https://api.unpaywall.org'

  def self.get_pdf_link(doi, email = '<EMAIL>')
    response = get("/v2/#{doi}?email=#{email}")
    
    if response.success?
      data = response.parsed_response
      
      # En iyi PDF linkini bul
      if data['is_oa'] && data['best_oa_location']
        {
          is_open_access: true,
          pdf_url: data['best_oa_location']['url_for_pdf'],
          host_type: data['best_oa_location']['host_type'],
          license: data['best_oa_location']['license']
        }
      else
        { is_open_access: false, pdf_url: nil }
      end
    else
      { is_open_access: false, pdf_url: nil, error: "Unpaywall API error: #{response.code}" }
    end
  rescue => e
    { is_open_access: false, pdf_url: nil, error: e.message }
  end
end

# Mastra agent'ı çalıştırma servisi
class MastraService
  def self.analyze_doi(doi)
    logger = Logger.new(STDOUT)
    logger.info("Analyzing DOI: #{doi}")

    # CLI agent'ı çalıştır
    command = "cd .. && node cli-agent.js '#{doi.gsub("'", "\\'")}'"

    stdout, stderr, status = Open3.capture3(command)

    if status.success?
      # JSON'u parse etmeye çalış
      begin
        JSON.parse(stdout)
      rescue JSON::ParserError => e
        logger.error("JSON parse error: #{e.message}")
        logger.error("Raw output: #{stdout}")
        create_fallback_analysis(doi)
      end
    else
      logger.error("CLI agent failed: #{stderr}")
      logger.error("Raw output: #{stdout}")
      create_fallback_analysis(doi)
    end
  end
  
  private
  
  def self.create_fallback_analysis(doi)
    {
      'title' => 'Makale analizi yapılamadı',
      'authors' => 'Bilinmiyor',
      'journal' => 'Bilinmiyor',
      'year' => 'Bilinmiyor',
      'field' => 'Genel',
      'abstract' => 'Özet alınamadı',
      'findings' => '• Analiz yapılamadı\n• Lütfen daha sonra tekrar deneyin',
      'conclusion' => 'Sonuç analiz edilemedi',
      'methodology' => 'Metodoloji bilgisi alınamadı',
      'methods' => 'Yöntem bilgisi alınamadı',
      'dataQuality' => 'LIMITED',
      'limitations' => 'Sistem hatası nedeniyle analiz tamamlanamadı',
      'keywords' => '',
      'citationCount' => 0,
      'pmid' => '',
      'error' => 'Mastra agent analizi başarısız'
    }
  end
end

# Health check endpoint
get '/health' do
  content_type :json
  { status: 'OK', timestamp: Time.now.iso8601 }.to_json
end

# Ana endpoint - DOI analizi
get '/summary' do
  content_type :json
  
  doi = params['doi']
  
  if doi.nil? || doi.strip.empty?
    status 400
    return { error: 'DOI parametresi gerekli' }.to_json
  end
  
  # DOI'yi temizle
  clean_doi = doi.gsub(/^https?:\/\/(dx\.)?doi\.org\//, '')
  
  logger.info("Processing DOI: #{clean_doi}")
  
  begin
    # Paralel olarak Mastra analizi ve Unpaywall sorgusu yap
    analysis_thread = Thread.new { MastraService.analyze_doi(clean_doi) }
    unpaywall_thread = Thread.new { UnpaywallService.get_pdf_link(clean_doi) }
    
    # Sonuçları bekle
    analysis_result = analysis_thread.value
    unpaywall_result = unpaywall_thread.value
    
    # Sonuçları birleştir
    result = {
      doi: clean_doi,
      analysis: analysis_result,
      pdf_access: unpaywall_result,
      processed_at: Time.now.iso8601
    }
    
    logger.info("Analysis completed for DOI: #{clean_doi}")
    result.to_json
    
  rescue => e
    logger.error("Error processing DOI #{clean_doi}: #{e.message}")
    status 500
    {
      error: 'Makale analizi sırasında hata oluştu',
      details: e.message,
      doi: clean_doi
    }.to_json
  end
end

# CORS preflight
options '*' do
  response.headers['Allow'] = 'GET, POST, OPTIONS'
  response.headers['Access-Control-Allow-Headers'] = 'Authorization, Content-Type, Accept, X-User-Email, X-Auth-Token'
  response.headers['Access-Control-Allow-Origin'] = '*'
  200
end

# 404 handler
not_found do
  content_type :json
  { error: 'Endpoint bulunamadı' }.to_json
end

# Error handler
error do
  content_type :json
  { error: 'Sunucu hatası', details: env['sinatra.error'].message }.to_json
end

# Server başlatma bilgisi
if __FILE__ == $0
  puts "🚀 Ruby Sinatra Backend starting..."
  puts "📡 API Endpoint: http://localhost:4567/summary?doi=..."
  puts "🔍 Health Check: http://localhost:4567/health"
  puts "📚 Example: http://localhost:4567/summary?doi=10.1016/j.kint.2021.02.040"
end
