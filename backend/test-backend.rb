#!/usr/bin/env ruby

require 'net/http'
require 'json'
require 'uri'

def test_backend
  puts "🧪 Ruby Backend test başlıyor...\n"
  
  # Health check
  puts "🔍 Health check..."
  begin
    uri = URI('http://localhost:4567/health')
    response = Net::HTTP.get_response(uri)
    
    if response.code == '200'
      puts "✅ Health check başarılı"
      health_data = JSON.parse(response.body)
      puts "   Status: #{health_data['status']}"
      puts "   Time: #{health_data['timestamp']}"
    else
      puts "❌ Health check başarısız: #{response.code}"
      return false
    end
  rescue => e
    puts "❌ Health check hatası: #{e.message}"
    puts "   Backend çalışıyor mu? (ruby backend/app.rb)"
    return false
  end
  
  # DOI analizi
  puts "\n📡 DOI analizi test ediliyor..."
  test_doi = '10.1007/s44163-022-00022-8'
  puts "   Test DOI: #{test_doi}"
  
  begin
    uri = URI("http://localhost:4567/summary?doi=#{URI.encode_www_form_component(test_doi)}")
    
    puts "   İstek gönderiliyor..."
    start_time = Time.now
    
    response = Net::HTTP.get_response(uri)
    end_time = Time.now
    
    puts "   Süre: #{(end_time - start_time).round(2)} saniye"
    puts "   HTTP Status: #{response.code}"
    
    if response.code == '200'
      result = JSON.parse(response.body)
      
      puts "\n📊 Sonuçlar:"
      puts "   DOI: #{result['doi']}"
      puts "   Başlık: #{result['analysis']['title']}"
      puts "   Alan: #{result['analysis']['field']}"
      puts "   Veri Kalitesi: #{result['analysis']['dataQuality']}"
      puts "   PDF Erişimi: #{result['pdf_access']['is_open_access'] ? 'Evet' : 'Hayır'}"
      
      if result['pdf_access']['is_open_access'] && result['pdf_access']['pdf_url']
        puts "   PDF URL: #{result['pdf_access']['pdf_url']}"
      end
      
      puts "\n✅ DOI analizi başarılı!"
      return true
    else
      puts "❌ DOI analizi başarısız: #{response.code}"
      puts "   Response: #{response.body}"
      return false
    end
    
  rescue => e
    puts "❌ DOI analizi hatası: #{e.message}"
    return false
  end
end

def test_error_handling
  puts "\n🧪 Hata yönetimi test ediliyor..."
  
  # Geçersiz DOI
  begin
    uri = URI('http://localhost:4567/summary?doi=invalid-doi')
    response = Net::HTTP.get_response(uri)
    
    if response.code == '500' || response.code == '400'
      puts "✅ Geçersiz DOI için uygun hata döndü: #{response.code}"
    else
      puts "⚠️ Beklenmeyen response: #{response.code}"
    end
  rescue => e
    puts "❌ Hata testi başarısız: #{e.message}"
  end
  
  # Eksik DOI parametresi
  begin
    uri = URI('http://localhost:4567/summary')
    response = Net::HTTP.get_response(uri)
    
    if response.code == '400'
      puts "✅ Eksik DOI için uygun hata döndü: #{response.code}"
    else
      puts "⚠️ Beklenmeyen response: #{response.code}"
    end
  rescue => e
    puts "❌ Hata testi başarısız: #{e.message}"
  end
end

# Ana test
puts "🚀 Ruby Sinatra Backend Test Suite"
puts "=" * 50

success = test_backend

if success
  test_error_handling
  puts "\n🎉 Tüm testler tamamlandı!"
  puts "\n📝 Sonraki adımlar:"
  puts "   1. Frontend'i başlat: cd frontend && npm run dev"
  puts "   2. Tarayıcıda aç: http://localhost:3000"
  puts "   3. DOI gir ve test et"
else
  puts "\n💥 Test başarısız!"
  puts "\n🔧 Sorun giderme:"
  puts "   1. Backend çalışıyor mu? ruby backend/app.rb"
  puts "   2. Gerekli gem'ler yüklü mü? cd backend && bundle install"
  puts "   3. CLI agent çalışıyor mu? node cli-agent.js test-doi"
end
