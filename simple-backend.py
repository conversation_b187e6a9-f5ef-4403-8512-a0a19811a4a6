#!/usr/bin/env python3

import json
import subprocess
import time
import urllib.parse
import urllib.request
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading

class UnpaywallService:
    @staticmethod
    def get_comprehensive_data(doi, email='<EMAIL>'):
        """Unpaywall API ile kapsamlı makale verisi alma"""
        try:
            url = f'https://api.unpaywall.org/v2/{doi}?email={email}'

            with urllib.request.urlopen(url, timeout=15) as response:
                if response.status == 200:
                    data = json.loads(response.read().decode())

                    # Tüm OA lokasyonlarını topla
                    oa_locations = data.get('oa_locations', [])
                    pdf_urls = []

                    for location in oa_locations:
                        if location.get('url_for_pdf'):
                            pdf_urls.append({
                                'url': location['url_for_pdf'],
                                'host_type': location.get('host_type', 'unknown'),
                                'license': location.get('license'),
                                'repository_institution': location.get('repository_institution')
                            })

                    # En iyi PDF URL'yi seç
                    best_pdf = None
                    if data.get('best_oa_location') and data['best_oa_location'].get('url_for_pdf'):
                        best_pdf = {
                            'url': data['best_oa_location']['url_for_pdf'],
                            'host_type': data['best_oa_location'].get('host_type'),
                            'license': data['best_oa_location'].get('license')
                        }

                    return {
                        'is_open_access': data.get('is_oa', False),
                        'pdf_url': best_pdf['url'] if best_pdf else None,
                        'best_pdf': best_pdf,
                        'all_pdf_urls': pdf_urls,
                        'oa_date': data.get('oa_date'),
                        'journal_is_oa': data.get('journal_is_oa', False),
                        'journal_is_in_doaj': data.get('journal_is_in_doaj', False),
                        'publisher': data.get('publisher'),
                        'journal_name': data.get('journal_name'),
                        'journal_issns': data.get('journal_issns'),
                        'title': data.get('title'),
                        'year': data.get('year'),
                        'authors': [author.get('family', '') + ', ' + author.get('given', '') for author in data.get('z_authors', [])],
                        'doi_url': data.get('doi_url'),
                        'updated': data.get('updated'),
                        'has_repository_copy': data.get('has_repository_copy', False),
                        'genre': data.get('genre'),
                        'oa_locations_count': len(oa_locations)
                    }
                else:
                    return {'is_open_access': False, 'pdf_url': None, 'error': f'Unpaywall API error: {response.status}'}

        except Exception as e:
            print(f'Unpaywall API error: {e}')
            return {'is_open_access': False, 'pdf_url': None, 'error': str(e)}

    @staticmethod
    def get_full_text_if_available(pdf_url):
        """PDF'den metin çıkarma (basit implementasyon)"""
        try:
            if not pdf_url:
                return None

            # PDF'yi indirmeye çalış (sadece ilk birkaç KB)
            req = urllib.request.Request(pdf_url)
            req.add_header('User-Agent', 'Academic-Article-Analyzer/1.0')

            with urllib.request.urlopen(req, timeout=10) as response:
                if response.status == 200:
                    # PDF erişilebilir
                    content_type = response.headers.get('Content-Type', '')
                    content_length = response.headers.get('Content-Length', '0')

                    return {
                        'accessible': True,
                        'content_type': content_type,
                        'size_bytes': int(content_length) if content_length.isdigit() else 0,
                        'url': pdf_url
                    }
                else:
                    return {'accessible': False, 'error': f'HTTP {response.status}'}

        except Exception as e:
            print(f'PDF access check error: {e}')
            return {'accessible': False, 'error': str(e)}

class CLIAgentService:
    @staticmethod
    def analyze_doi(doi):
        """CLI Agent ile DOI analizi"""
        try:
            print(f'Analyzing DOI: {doi}')
            
            # CLI agent'ı çalıştır
            result = subprocess.run(
                ['node', 'cli-agent.js', doi],
                capture_output=True,
                text=True,
                timeout=30,
                cwd='.'
            )
            
            if result.returncode == 0:
                # JSON parse et
                try:
                    return json.loads(result.stdout)
                except json.JSONDecodeError as e:
                    print(f'JSON parse error: {e}')
                    print(f'Raw output: {result.stdout}')
                    return CLIAgentService.create_fallback_analysis(doi)
            else:
                print(f'CLI agent failed: {result.stderr}')
                return CLIAgentService.create_fallback_analysis(doi)
                
        except subprocess.TimeoutExpired:
            print('CLI agent timeout')
            return CLIAgentService.create_fallback_analysis(doi)
        except Exception as e:
            print(f'CLI agent error: {e}')
            return CLIAgentService.create_fallback_analysis(doi)
    
    @staticmethod
    def create_fallback_analysis(doi):
        """Fallback analiz"""
        return {
            'title': 'Makale analizi yapılamadı',
            'authors': 'Bilinmiyor',
            'journal': 'Bilinmiyor',
            'year': 'Bilinmiyor',
            'field': 'Genel',
            'abstract': 'Özet alınamadı',
            'findings': '• Analiz yapılamadı\n• Lütfen daha sonra tekrar deneyin',
            'conclusion': 'Sonuç analiz edilemedi',
            'methodology': 'Metodoloji bilgisi alınamadı',
            'methods': 'Yöntem bilgisi alınamadı',
            'dataQuality': 'LIMITED',
            'limitations': 'Sistem hatası nedeniyle analiz tamamlanamadı',
            'keywords': '',
            'citationCount': 0,
            'pmid': '',
            'error': 'CLI agent analizi başarısız'
        }

class APIHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """CORS preflight"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_GET(self):
        """GET istekleri"""
        if self.path == '/health':
            self.handle_health()
        elif self.path.startswith('/summary'):
            self.handle_summary()
        else:
            self.send_error(404, 'Endpoint bulunamadı')
    
    def handle_health(self):
        """Health check"""
        response = {
            'status': 'OK',
            'timestamp': time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime())
        }

        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def handle_summary(self):
        """DOI analizi"""
        try:
            # URL'den DOI parametresini al
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            
            if 'doi' not in query_params:
                self.send_error(400, 'DOI parametresi gerekli')
                return
            
            doi = query_params['doi'][0]
            clean_doi = doi.replace('https://doi.org/', '').replace('http://dx.doi.org/', '')
            
            print(f'Processing DOI: {clean_doi}')
            
            # Paralel olarak analiz ve PDF kontrolü yap
            analysis_result = [None]
            unpaywall_result = [None]
            
            def run_analysis():
                analysis_result[0] = CLIAgentService.analyze_doi(clean_doi)
            
            def run_unpaywall():
                unpaywall_result[0] = UnpaywallService.get_comprehensive_data(clean_doi)
            
            # Thread'leri başlat
            analysis_thread = threading.Thread(target=run_analysis)
            unpaywall_thread = threading.Thread(target=run_unpaywall)
            
            analysis_thread.start()
            unpaywall_thread.start()
            
            # Thread'leri bekle
            analysis_thread.join(timeout=30)
            unpaywall_thread.join(timeout=10)
            
            # Sonuçları birleştir
            result = {
                'doi': clean_doi,
                'analysis': analysis_result[0] or CLIAgentService.create_fallback_analysis(clean_doi),
                'pdf_access': unpaywall_result[0] or {'is_open_access': False, 'pdf_url': None},
                'processed_at': time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime())
            }
            
            print(f'Analysis completed for DOI: {clean_doi}')
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode())
            
        except Exception as e:
            print(f'Error: {e}')
            error_response = {
                'error': 'Makale analizi sırasında hata oluştu',
                'details': str(e)
            }
            
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode())

def run_server():
    server_address = ('', 4567)
    httpd = HTTPServer(server_address, APIHandler)
    
    print("🚀 Simple Python Backend starting...")
    print("📡 API Endpoint: http://localhost:4567/summary?doi=...")
    print("🔍 Health Check: http://localhost:4567/health")
    print("📚 Example: http://localhost:4567/summary?doi=10.1016/j.kint.2021.02.040")
    print("Press Ctrl+C to stop")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.shutdown()

if __name__ == '__main__':
    run_server()
