import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { lookupDOI } from '@uwdata/citation-query';

// Crossref API ile ek veri çekme
async function fetchCrossrefData(doi: string) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 saniye timeout

    const response = await fetch(`https://api.crossref.org/works/${doi}`, {
      headers: {
        'User-Agent': 'DOI-Summary-Tool/1.0 (mailto:<EMAIL>)'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Crossref API error: ${response.status}`);
    }

    const data = await response.json();
    return data.message;
  } catch (error) {
    console.warn('Crossref API failed:', error.message);
    return null;
  }
}

// PubMed API ile ek veri çekme (tı<PERSON><PERSON> ma<PERSON><PERSON>)
async function fetchPubMedData(doi: string) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 saniye timeout

    // DOI ile PubMed ID bulma
    const searchResponse = await fetch(
      `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=${encodeURIComponent(doi)}&retmode=json`,
      { signal: controller.signal }
    );

    if (!searchResponse.ok) {
      clearTimeout(timeoutId);
      return null;
    }

    const searchData = await searchResponse.json();
    const pmid = searchData.esearchresult?.idlist?.[0];

    if (!pmid) {
      clearTimeout(timeoutId);
      return null;
    }

    // PubMed detayları çekme
    const detailResponse = await fetch(
      `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=${pmid}&retmode=xml`,
      { signal: controller.signal }
    );

    clearTimeout(timeoutId);

    if (!detailResponse.ok) return null;

    const xmlText = await detailResponse.text();

    // Basit XML parsing (abstract çıkarma)
    const abstractMatch = xmlText.match(/<AbstractText[^>]*>(.*?)<\/AbstractText>/s);
    const abstract = abstractMatch ? abstractMatch[1].replace(/<[^>]*>/g, '') : null;

    const keywordsMatch = xmlText.match(/<Keyword[^>]*>(.*?)<\/Keyword>/g);
    const keywords = keywordsMatch ? keywordsMatch.map(k => k.replace(/<[^>]*>/g, '')).join(', ') : null;

    return { abstract, keywords, pmid };
  } catch (error) {
    console.warn('PubMed API failed:', error.message);
    return null;
  }
}

// Semantic Scholar API ile ek veri çekme
async function fetchSemanticScholarData(doi: string) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 6000); // 6 saniye timeout (daha kısa)

    const response = await fetch(
      `https://api.semanticscholar.org/graph/v1/paper/DOI:${doi}?fields=title,abstract,authors,year,journal,citationCount,influentialCitationCount,fieldsOfStudy`,
      {
        signal: controller.signal,
        headers: {
          'User-Agent': 'DOI-Summary-Tool/1.0'
        }
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      console.warn(`Semantic Scholar API returned ${response.status}`);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.warn('Semantic Scholar API failed:', error.message);
    return null;
  }
}

export const articleSummaryTool = createTool({
  id: 'get-article-summary',
  description: 'Fetch comprehensive article metadata, abstract, and full text by DOI using multiple sources',
  inputSchema: z.object({
    doi: z.string().describe('Digital Object Identifier of the article (e.g., 10.1016/j.kint.2021.02.040)'),
  }),
  outputSchema: z.object({
    title: z.string(),
    authors: z.string(),
    journal: z.string(),
    year: z.string(),
    abstract: z.string(),
    fulltext: z.string().optional(),
    keywords: z.string().optional(),
    type: z.string().optional(),
    url: z.string().optional(),
    publisher: z.string().optional(),
    citationCount: z.number().optional(),
    fieldsOfStudy: z.string().optional(),
    pmid: z.string().optional(),
  }),
  execute: async ({ context }) => {
    try {
      const doi = context.doi.replace(/^https?:\/\/(dx\.)?doi\.org\//, '');
      console.log(`Fetching comprehensive article data for DOI: ${doi}`);

      // Paralel olarak birden fazla kaynaktan veri çek (timeout ile)
      console.log('Fetching data from multiple sources...');
      const [primaryData, crossrefData, pubmedData, semanticData] = await Promise.allSettled([
        Promise.race([
          lookupDOI(doi),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Primary lookup timeout')), 12000))
        ]),
        fetchCrossrefData(doi),
        fetchPubMedData(doi),
        fetchSemanticScholarData(doi)
      ]);

      // Ana veri kaynağı
      const data = primaryData.status === 'fulfilled' ? primaryData.value : null;
      const crossref = crossrefData.status === 'fulfilled' ? crossrefData.value : null;
      const pubmed = pubmedData.status === 'fulfilled' ? pubmedData.value : null;
      const semantic = semanticData.status === 'fulfilled' ? semanticData.value : null;

      // Hangi kaynakların başarılı olduğunu logla
      console.log(`Data sources status: Primary=${!!data}, Crossref=${!!crossref}, PubMed=${!!pubmed}, Semantic=${!!semantic}`);

      // En az bir kaynak başarılı olmalı
      if (!data && !crossref && !semantic) {
        throw new Error(`DOI ${doi} için hiçbir kaynaktan makale bulunamadı`);
      }

      // En iyi veriyi birleştir
      const title = semantic?.title ||
                   crossref?.title?.[0] ||
                   data?.title?.[0] ||
                   data?.title ||
                   'Başlık bulunamadı';

      // Yazarları formatla
      let authors = 'Yazar bilgisi bulunamadı';
      if (semantic?.authors) {
        authors = semantic.authors.map((a: any) => a.name).join(', ');
      } else if (data?.author) {
        authors = data.author.map((a: any) => `${a.given || ''} ${a.family || ''}`.trim())
                            .filter((name: string) => name.length > 0).join(', ');
      } else if (crossref?.author) {
        authors = crossref.author.map((a: any) => `${a.given || ''} ${a.family || ''}`.trim())
                                .filter((name: string) => name.length > 0).join(', ');
      }

      // Dergi adını al
      const journal = semantic?.journal?.name ||
                     crossref?.['container-title']?.[0] ||
                     data?.['container-title']?.[0] ||
                     data?.['container-title'] ||
                     data?.journal ||
                     crossref?.publisher ||
                     data?.publisher ||
                     'Dergi bilgisi bulunamadı';

      // Yayın yılını al
      const year = semantic?.year?.toString() ||
                   crossref?.issued?.['date-parts']?.[0]?.[0]?.toString() ||
                   data?.issued?.['date-parts']?.[0]?.[0]?.toString() ||
                   data?.published?.['date-parts']?.[0]?.[0]?.toString() ||
                   data?.created?.['date-parts']?.[0]?.[0]?.toString() ||
                   data?.issued?.date ||
                   'Yıl bilgisi bulunamadı';

      // Abstract - en detaylısını al
      const abstract = pubmed?.abstract ||
                      semantic?.abstract ||
                      crossref?.abstract ||
                      data?.abstract ||
                      'Özet bulunamadı';

      // Anahtar kelimeleri al
      const keywords = pubmed?.keywords ||
                      semantic?.fieldsOfStudy?.join(', ') ||
                      data?.subject?.join(', ') ||
                      data?.categories?.join(', ') ||
                      '';

      // URL oluştur
      const url = crossref?.URL || data?.URL || `https://doi.org/${doi}`;

      return {
        title,
        authors,
        journal,
        year,
        abstract,
        fulltext: data?.fulltext || '',
        keywords,
        type: crossref?.type || data?.type || 'journal-article',
        url,
        publisher: crossref?.publisher || data?.publisher || '',
        citationCount: semantic?.citationCount || 0,
        fieldsOfStudy: semantic?.fieldsOfStudy?.join(', ') || '',
        pmid: pubmed?.pmid || '',
      };
    } catch (error) {
      console.error('DOI lookup error:', error);
      throw new Error(`DOI arama hatası: ${error.message}`);
    }
  },
});
