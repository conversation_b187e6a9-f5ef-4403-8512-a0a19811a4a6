// SVG oluşturma testi
import { SVG, registerWindow } from '@svgdotjs/svg.js';
import { createSVGWindow } from 'svgdom';
import fs from 'fs';

function createMedicalSummarySVG({ title, authors, journal, year, abstract, findings, conclusion, methodology, statistics }) {
  const window = createSVGWindow();
  const document = window.document;
  registerWindow(window, document);
  
  // Ana SVG oluştur
  const draw = SVG(document.documentElement).size(1000, 700);
  
  // Arkaplan
  draw.rect(1000, 700).fill('#f8f9fa');
  
  // Başlık alanı
  const headerBg = draw.rect(1000, 80).fill('#3d6a80');
  draw.text(title)
    .move(20, 20)
    .font({ size: 24, weight: 'bold', family: 'Arial' })
    .fill('#ffffff')
    .width(960);
  
  // İçerik alanı
  const contentY = 100;
  
  // Sol taraf - Metodoloji ve İstatistik
  const leftX = 20;
  
  // Metodoloji kutusu
  draw.rect(300, 120).move(leftX, contentY).fill('#e9ecef').radius(5);
  draw.text('Metodoloji')
    .move(leftX + 10, contentY + 10)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#212529');
  
  draw.text(methodology)
    .move(leftX + 10, contentY + 40)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);
  
  // İstatistik kutusu
  draw.rect(300, 120).move(leftX, contentY + 140).fill('#e9ecef').radius(5);
  draw.text('İstatistiksel Analiz')
    .move(leftX + 10, contentY + 150)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#212529');
  
  draw.text(statistics)
    .move(leftX + 10, contentY + 180)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);
  
  // Makale bilgileri
  draw.rect(300, 120).move(leftX, contentY + 280).fill('#e9ecef').radius(5);
  draw.text('Makale Bilgileri')
    .move(leftX + 10, contentY + 290)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#212529');
  
  draw.text(`Yazarlar: ${authors}`)
    .move(leftX + 10, contentY + 320)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);
  
  draw.text(`Dergi: ${journal} (${year})`)
    .move(leftX + 10, contentY + 350)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);
  
  // Sağ taraf - Özet, Bulgular ve Sonuç
  const rightX = 340;
  
  // Özet kutusu
  draw.rect(640, 150).move(rightX, contentY).fill('#d1e7dd').radius(5);
  draw.text('Özet')
    .move(rightX + 10, contentY + 10)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#0f5132');
  
  draw.text(abstract.slice(0, 500) + (abstract.length > 500 ? '...' : ''))
    .move(rightX + 10, contentY + 40)
    .font({ size: 14, family: 'Arial' })
    .fill('#0f5132')
    .width(620);
  
  // Ana Bulgular kutusu
  draw.rect(640, 180).move(rightX, contentY + 170).fill('#cfe2ff').radius(5);
  draw.text('Ana Bulgular')
    .move(rightX + 10, contentY + 180)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#084298');
  
  draw.text(findings)
    .move(rightX + 10, contentY + 210)
    .font({ size: 14, family: 'Arial' })
    .fill('#084298')
    .width(620);
  
  // Sonuç kutusu
  draw.rect(640, 130).move(rightX, contentY + 370).fill('#f8d7da').radius(5);
  draw.text('Sonuç')
    .move(rightX + 10, contentY + 380)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#842029');
  
  draw.text(conclusion)
    .move(rightX + 10, contentY + 410)
    .font({ size: 14, family: 'Arial' })
    .fill('#842029')
    .width(620);
  
  // Alt bilgi
  draw.text('Bu özet yapay zeka tarafından oluşturulmuştur. Klinik kararlar için orijinal makaleye başvurunuz.')
    .move(20, 670)
    .font({ size: 12, family: 'Arial', style: 'italic' })
    .fill('#6c757d');
  
  return draw.svg();
}

// Test verisi
const testData = {
  title: "Paclitaxel-coated balloons provide no benefit for arteriovenous fistulas",
  authors: "Narayan Karunanithy, Emily J. Robinson, Farhan Ahmad, et al.",
  journal: "Kidney International",
  year: "2021",
  abstract: "Bu çalışma, hemodiyaliz arteriyovenöz fistüllerinde paklitaksel kaplı balonların etkinliğini değerlendiren çok merkezli randomize kontrollü bir çalışmadır. Sonuçlar, paklitaksel kaplı balonların geleneksel balon anjiyoplastiye kıyasla anlamlı bir fayda sağlamadığını göstermektedir.",
  findings: "• Paklitaksel kaplı balon grubu ile kontrol grubu arasında primer açıklık süresinde anlamlı fark bulunmadı\n• 6 aylık açıklık oranları benzer bulundu\n• Komplikasyon oranları gruplar arasında farklı değildi\n• Maliyet-etkinlik analizi paklitaksel lehine sonuç vermedi",
  conclusion: "Hemodiyaliz arteriyovenöz fistül stenozlarında paklitaksel kaplı balon anjiyoplasti, geleneksel balon anjiyoplastiye kıyasla üstünlük göstermemektedir. Bu bulgular, rutin klinik pratikte paklitaksel kaplı balon kullanımını desteklememektedir.",
  methodology: "Çok merkezli, randomize, kontrollü çalışma tasarımı kullanıldı. Hastalar paklitaksel kaplı balon veya geleneksel balon anjiyoplasti gruplarına randomize edildi.",
  statistics: "Kaplan-Meier sağkalım analizi ve log-rank testi kullanıldı. P<0.05 istatistiksel olarak anlamlı kabul edildi."
};

console.log('SVG oluşturuluyor...');
const svg = createMedicalSummarySVG(testData);

fs.writeFileSync('test-summary.svg', svg);
console.log('SVG "test-summary.svg" dosyasına kaydedildi.');

console.log('SVG test başarıyla tamamlandı!');
