"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_DOIForm_tsx"],{

/***/ "(app-pages-browser)/./app/components/DOIForm.tsx":
/*!************************************!*\
  !*** ./app/components/DOIForm.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DOIForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SimpleIcons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SimpleIcons */ \"(app-pages-browser)/./app/components/SimpleIcons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DOIForm(param) {\n    let { onSubmit, isLoading } = param;\n    _s();\n    const [doi, setDoi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (doi.trim() && !isLoading) {\n            onSubmit(doi.trim());\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"doi\",\n                                className: \"sr-only\",\n                                children: \"DOI Number\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"doi\",\n                                value: doi,\n                                onChange: (e)=>setDoi(e.target.value),\n                                placeholder: \"DOI numarası girin (\\xf6rn: 10.1016/j.kint.2021.02.040)\",\n                                className: \"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 placeholder-gray-500 bg-white\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: !doi.trim() || isLoading,\n                        className: \"px-6 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                        children: [\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_2__.Loader2Icon, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_2__.SearchIcon, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isLoading ? \"Analyzing...\" : \"Analyze\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Akademik makaleyi analiz etmek i\\xe7in DOI numarası girin. Sistem t\\xfcm akademik alanlardan makaleleri destekler.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_s(DOIForm, \"UdPAJgBp68O8ByFRf97JfOUsXOM=\");\n_c = DOIForm;\nvar _c;\n$RefreshReg$(_c, \"DOIForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/DOIForm.tsx\n"));

/***/ })

}]);