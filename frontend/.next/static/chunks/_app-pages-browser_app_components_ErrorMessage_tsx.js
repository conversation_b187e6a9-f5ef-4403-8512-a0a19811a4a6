"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_ErrorMessage_tsx"],{

/***/ "(app-pages-browser)/./app/components/ErrorMessage.tsx":
/*!*****************************************!*\
  !*** ./app/components/ErrorMessage.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ErrorMessage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _SimpleIcons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SimpleIcons */ \"(app-pages-browser)/./app/components/SimpleIcons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ErrorMessage(param) {\n    let { message, onRetry } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 text-red-600 flex-shrink-0 mt-0.5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_1__.AlertTriangleIcon, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-red-900 mb-2\",\n                            children: \"Analiz Başarısız\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 text-sm text-red-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Olası \\xe7\\xf6z\\xfcmler:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 16\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"DOI numarasının doğru olduğunu kontrol edin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"B\\xfcy\\xfck bir yayıncıdan farklı bir DOI deneyin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Bir s\\xfcre bekleyip tekrar deneyin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"İnternet bağlantınızı kontrol edin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onRetry,\n                            className: \"mt-4 inline-flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_1__.RefreshCwIcon, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Tekrar Dene\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = ErrorMessage;\nvar _c;\n$RefreshReg$(_c, \"ErrorMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ErrorMessage.tsx\n"));

/***/ })

}]);