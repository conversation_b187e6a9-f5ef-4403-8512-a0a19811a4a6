"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_app_components_DOIForm_tsx";
exports.ids = ["_ssr_app_components_DOIForm_tsx"];
exports.modules = {

/***/ "(ssr)/./app/components/DOIForm.tsx":
/*!************************************!*\
  !*** ./app/components/DOIForm.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DOIForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SimpleIcons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SimpleIcons */ \"(ssr)/./app/components/SimpleIcons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DOIForm({ onSubmit, isLoading }) {\n    const [doi, setDoi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (doi.trim() && !isLoading) {\n            onSubmit(doi.trim());\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"doi\",\n                                className: \"sr-only\",\n                                children: \"DOI Number\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"doi\",\n                                value: doi,\n                                onChange: (e)=>setDoi(e.target.value),\n                                placeholder: \"DOI numarası girin (\\xf6rn: 10.1016/j.kint.2021.02.040)\",\n                                className: \"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 placeholder-gray-500 bg-white\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: !doi.trim() || isLoading,\n                        className: \"px-6 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                        children: [\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_2__.Loader2Icon, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_2__.SearchIcon, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isLoading ? \"Analyzing...\" : \"Analyze\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Akademik makaleyi analiz etmek i\\xe7in DOI numarası girin. Sistem t\\xfcm akademik alanlardan makaleleri destekler.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\DOIForm.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/DOIForm.tsx\n");

/***/ })

};
;