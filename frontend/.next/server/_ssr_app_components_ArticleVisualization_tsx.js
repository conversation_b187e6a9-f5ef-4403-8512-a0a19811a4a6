"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_app_components_ArticleVisualization_tsx";
exports.ids = ["_ssr_app_components_ArticleVisualization_tsx"];
exports.modules = {

/***/ "(ssr)/./app/components/ArticleVisualization.tsx":
/*!*************************************************!*\
  !*** ./app/components/ArticleVisualization.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArticleVisualization)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SimpleIcons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SimpleIcons */ \"(ssr)/./app/components/SimpleIcons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ArticleVisualization({ analysis }) {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"visual\");\n    // Alan bazlı renk seçimi\n    const getFieldColor = (field)=>{\n        const colors = {\n            \"Tıp\": \"from-red-500 to-red-600\",\n            \"M\\xfchendislik\": \"from-orange-500 to-orange-600\",\n            \"Fizik\": \"from-purple-500 to-purple-600\",\n            \"Kimya\": \"from-teal-500 to-teal-600\",\n            \"Bilgisayar\": \"from-blue-500 to-blue-600\",\n            \"Sosyal Bilimler\": \"from-green-500 to-green-600\",\n            \"Matematik\": \"from-indigo-500 to-indigo-600\",\n            \"Biyoloji\": \"from-emerald-500 to-emerald-600\",\n            \"Genel\": \"from-gray-500 to-gray-600\"\n        };\n        return colors[field] || colors[\"Genel\"];\n    };\n    // Metin sarma fonksiyonu\n    const wrapText = (text, maxLength)=>{\n        if (!text) return [\n            \"\"\n        ];\n        const words = text.split(\" \");\n        const lines = [];\n        let currentLine = \"\";\n        for (const word of words){\n            if ((currentLine + word).length <= maxLength) {\n                currentLine += (currentLine ? \" \" : \"\") + word;\n            } else {\n                if (currentLine) {\n                    lines.push(currentLine);\n                    currentLine = word;\n                } else {\n                    lines.push(word.slice(0, maxLength));\n                }\n            }\n        }\n        if (currentLine) {\n            lines.push(currentLine);\n        }\n        return lines;\n    };\n    // HTML entities'leri temizle ve Türkçe karakterleri koru\n    const cleanText = (text)=>{\n        if (!text) return \"\";\n        return text.replace(/<[^>]*>/g, \"\") // HTML taglarını kaldır\n        .replace(/&lt;/g, \"<\").replace(/&gt;/g, \">\").replace(/&amp;/g, \"&\").replace(/&quot;/g, '\"').replace(/&#39;/g, \"'\").replace(/&nbsp;/g, \" \")// Türkçe karakterleri koru\n        .replace(/&ccedil;/g, \"\\xe7\").replace(/&Ccedil;/g, \"\\xc7\").replace(/&#231;/g, \"\\xe7\").replace(/&#199;/g, \"\\xc7\").replace(/&gbreve;/g, \"ğ\").replace(/&Gbreve;/g, \"Ğ\").replace(/&#287;/g, \"ğ\").replace(/&#286;/g, \"Ğ\").replace(/&idot;/g, \"ı\").replace(/&#305;/g, \"ı\").replace(/&Idot;/g, \"İ\").replace(/&#304;/g, \"İ\").replace(/&ouml;/g, \"\\xf6\").replace(/&Ouml;/g, \"\\xd6\").replace(/&#246;/g, \"\\xf6\").replace(/&#214;/g, \"\\xd6\").replace(/&scedil;/g, \"ş\").replace(/&Scedil;/g, \"Ş\").replace(/&#351;/g, \"ş\").replace(/&#350;/g, \"Ş\").replace(/&uuml;/g, \"\\xfc\").replace(/&Uuml;/g, \"\\xdc\").replace(/&#252;/g, \"\\xfc\").replace(/&#220;/g, \"\\xdc\").trim();\n    };\n    const generateSVG = ()=>{\n        const fieldColor = analysis.field === \"Tıp\" ? \"#dc3545\" : analysis.field === \"M\\xfchendislik\" ? \"#fd7e14\" : analysis.field === \"Fizik\" ? \"#6f42c1\" : analysis.field === \"Kimya\" ? \"#20c997\" : analysis.field === \"Bilgisayar\" ? \"#0d6efd\" : analysis.field === \"Sosyal Bilimler\" ? \"#198754\" : \"#3d6a80\";\n        const qualityColor = analysis.dataQuality === \"FULL\" ? \"#198754\" : analysis.dataQuality === \"PARTIAL\" ? \"#fd7e14\" : \"#dc3545\";\n        // Metinleri temizle ve sarma\n        const cleanTitle = cleanText(analysis.title);\n        const titleLines = wrapText(cleanTitle, 70);\n        const cleanAbstract = cleanText(analysis.abstract);\n        const abstractLines = wrapText(cleanAbstract, 80);\n        const cleanFindings = cleanText(analysis.findings.replace(/\\n/g, \" \"));\n        const findingsLines = wrapText(cleanFindings, 120);\n        const cleanMethodology = cleanText(analysis.methodology);\n        const methodologyLines = wrapText(cleanMethodology, 50);\n        const cleanConclusion = cleanText(analysis.conclusion);\n        const conclusionLines = wrapText(cleanConclusion, 60);\n        const cleanAuthors = cleanText(analysis.authors);\n        const authorLines = wrapText(cleanAuthors, 50);\n        const cleanJournal = cleanText(analysis.journal);\n        const cleanKeywords = cleanText(analysis.keywords);\n        // SVG metinlerini XML-safe hale getir\n        const xmlSafe = (text)=>{\n            return text.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n        };\n        return `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"1000\" height=\"800\" viewBox=\"0 0 1000 800\">\n        <defs>\n          <linearGradient id=\"bgGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n            <stop offset=\"0%\" style=\"stop-color:#f8f9fa;stop-opacity:1\" />\n            <stop offset=\"100%\" style=\"stop-color:#e9ecef;stop-opacity:1\" />\n          </linearGradient>\n          <linearGradient id=\"headerGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n            <stop offset=\"0%\" style=\"stop-color:${fieldColor};stop-opacity:1\" />\n            <stop offset=\"100%\" style=\"stop-color:${fieldColor}dd;stop-opacity:1\" />\n          </linearGradient>\n        </defs>\n\n        <!-- Background -->\n        <rect width=\"1000\" height=\"800\" fill=\"url(#bgGradient)\"/>\n\n        <!-- Header -->\n        <rect width=\"1000\" height=\"100\" fill=\"url(#headerGradient)\"/>\n        ${titleLines.slice(0, 2).map((line, index)=>`<text x=\"20\" y=\"${30 + index * 25}\" font-family=\"Arial, sans-serif\" font-size=\"20\" font-weight=\"bold\" fill=\"white\">${xmlSafe(line)}</text>`).join(\"\\n        \")}\n        <text x=\"20\" y=\"85\" font-family=\"Arial, sans-serif\" font-size=\"14\" fill=\"white\" opacity=\"0.9\">\n          📚 ${xmlSafe(analysis.field)}\n        </text>\n\n        <!-- Data Quality -->\n        <rect x=\"20\" y=\"120\" width=\"300\" height=\"40\" fill=\"${qualityColor}\" rx=\"5\"/>\n        <text x=\"30\" y=\"145\" font-family=\"Arial, sans-serif\" font-size=\"16\" font-weight=\"bold\" fill=\"white\">\n          Veri Kalitesi: ${xmlSafe(analysis.dataQuality)}\n        </text>\n\n        <!-- Article Info -->\n        <rect x=\"20\" y=\"180\" width=\"460\" height=\"160\" fill=\"#e9ecef\" rx=\"5\"/>\n        <text x=\"30\" y=\"205\" font-family=\"Arial, sans-serif\" font-size=\"18\" font-weight=\"bold\" fill=\"#212529\">\n          Makale Bilgileri\n        </text>\n        <text x=\"30\" y=\"230\" font-family=\"Arial, sans-serif\" font-size=\"14\" fill=\"#212529\">\n          Yazarlar:\n        </text>\n        ${authorLines.slice(0, 2).map((line, index)=>`<text x=\"30\" y=\"${250 + index * 18}\" font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"#212529\">${xmlSafe(line)}</text>`).join(\"\\n        \")}\n        <text x=\"30\" y=\"290\" font-family=\"Arial, sans-serif\" font-size=\"14\" fill=\"#212529\">\n          Dergi: ${xmlSafe(cleanJournal.slice(0, 40))}${cleanJournal.length > 40 ? \"...\" : \"\"} (${xmlSafe(analysis.year)})\n        </text>\n        ${cleanKeywords ? `<text x=\"30\" y=\"315\" font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"#6c757d\" font-style=\"italic\">\n          Anahtar Kelimeler: ${xmlSafe(cleanKeywords.slice(0, 50))}${cleanKeywords.length > 50 ? \"...\" : \"\"}\n        </text>` : \"\"}\n\n        <!-- Abstract -->\n        <rect x=\"500\" y=\"180\" width=\"480\" height=\"160\" fill=\"#d1e7dd\" rx=\"5\"/>\n        <text x=\"510\" y=\"205\" font-family=\"Arial, sans-serif\" font-size=\"18\" font-weight=\"bold\" fill=\"#0f5132\">\n          Özet\n        </text>\n        ${abstractLines.slice(0, 6).map((line, index)=>`<text x=\"510\" y=\"${230 + index * 18}\" font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"#0f5132\">${xmlSafe(line)}</text>`).join(\"\\n        \")}\n\n        <!-- Findings -->\n        <rect x=\"20\" y=\"360\" width=\"960\" height=\"140\" fill=\"#cfe2ff\" rx=\"5\"/>\n        <text x=\"30\" y=\"385\" font-family=\"Arial, sans-serif\" font-size=\"18\" font-weight=\"bold\" fill=\"#084298\">\n          Ana Bulgular\n        </text>\n        ${findingsLines.slice(0, 5).map((line, index)=>`<text x=\"30\" y=\"${410 + index * 18}\" font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"#084298\">• ${xmlSafe(line)}</text>`).join(\"\\n        \")}\n\n        <!-- Methodology -->\n        <rect x=\"20\" y=\"520\" width=\"460\" height=\"120\" fill=\"#fff3cd\" rx=\"5\"/>\n        <text x=\"30\" y=\"545\" font-family=\"Arial, sans-serif\" font-size=\"18\" font-weight=\"bold\" fill=\"#664d03\">\n          Metodoloji\n        </text>\n        ${methodologyLines.slice(0, 4).map((line, index)=>`<text x=\"30\" y=\"${570 + index * 18}\" font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"#664d03\">${xmlSafe(line)}</text>`).join(\"\\n        \")}\n\n        <!-- Conclusion -->\n        <rect x=\"500\" y=\"520\" width=\"480\" height=\"120\" fill=\"#f8d7da\" rx=\"5\"/>\n        <text x=\"510\" y=\"545\" font-family=\"Arial, sans-serif\" font-size=\"18\" font-weight=\"bold\" fill=\"#842029\">\n          Sonuç\n        </text>\n        ${conclusionLines.slice(0, 4).map((line, index)=>`<text x=\"510\" y=\"${570 + index * 18}\" font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"#842029\">${xmlSafe(line)}</text>`).join(\"\\n        \")}\n\n        <!-- Footer -->\n        <text x=\"20\" y=\"760\" font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"#6c757d\" font-style=\"italic\">\n          Bu özet yapay zeka tarafından oluşturulmuştur. Detaylı bilgi için orijinal makaleye başvurunuz.\n        </text>\n      </svg>`;\n    };\n    const downloadSVG = ()=>{\n        const svgContent = generateSVG();\n        const blob = new Blob([\n            svgContent\n        ], {\n            type: \"image/svg+xml\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = `article-summary-${analysis.field.toLowerCase()}.svg`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex space-x-8 px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"visual\"),\n                            className: `py-4 px-1 border-b-2 font-medium text-sm ${activeTab === \"visual\" ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_2__.EyeIcon, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"G\\xf6rsel \\xd6zet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"data\"),\n                            className: `py-4 px-1 border-b-2 font-medium text-sm ${activeTab === \"data\" ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_2__.CodeIcon, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2\",\n                                    children: \"Detaylı Veriler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    activeTab === \"visual\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Makale \\xd6zet G\\xf6rselleştirmesi\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadSVG,\n                                        className: \"inline-flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_2__.DownloadIcon, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"SVG İndir\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-auto\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: generateSVG()\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"data\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"Detaylı Analiz Verileri\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-green-900 mb-2\",\n                                                children: \"\\xd6zet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-green-300 scrollbar-track-green-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-800 text-sm pr-2\",\n                                                    children: analysis.abstract\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-900 mb-2\",\n                                                children: \"Metodoloji\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-blue-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-800 text-sm pr-2\",\n                                                    children: analysis.methodology\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-purple-900 mb-2\",\n                                                children: \"Y\\xf6ntemler\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-300 scrollbar-track-purple-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-800 text-sm pr-2\",\n                                                    children: analysis.methods\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-yellow-900 mb-2\",\n                                                children: \"Sınırlamalar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-yellow-300 scrollbar-track-yellow-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-yellow-800 text-sm pr-2\",\n                                                    children: analysis.limitations\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-indigo-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-indigo-900 mb-2\",\n                                        children: \"Ana Bulgular\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-indigo-300 scrollbar-track-indigo-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-indigo-800 text-sm whitespace-pre-line pr-2\",\n                                            children: analysis.findings\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-red-900 mb-2\",\n                                        children: \"Sonu\\xe7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-red-300 scrollbar-track-red-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-800 text-sm pr-2\",\n                                            children: analysis.conclusion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ArticleVisualization.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ArticleVisualization.tsx\n");

/***/ })

};
;