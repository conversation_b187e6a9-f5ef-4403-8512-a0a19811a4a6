"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_app_components_ErrorMessage_tsx";
exports.ids = ["_ssr_app_components_ErrorMessage_tsx"];
exports.modules = {

/***/ "(ssr)/./app/components/ErrorMessage.tsx":
/*!*****************************************!*\
  !*** ./app/components/ErrorMessage.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ErrorMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _SimpleIcons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SimpleIcons */ \"(ssr)/./app/components/SimpleIcons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ErrorMessage({ message, onRetry }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-6 w-6 text-red-600 flex-shrink-0 mt-0.5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_1__.AlertTriangleIcon, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-red-900 mb-2\",\n                            children: \"Analiz Başarısız\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700 mb-4\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 text-sm text-red-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Olası \\xe7\\xf6z\\xfcmler:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 16\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"DOI numarasının doğru olduğunu kontrol edin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"B\\xfcy\\xfck bir yayıncıdan farklı bir DOI deneyin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Bir s\\xfcre bekleyip tekrar deneyin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"İnternet bağlantınızı kontrol edin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onRetry,\n                            className: \"mt-4 inline-flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleIcons__WEBPACK_IMPORTED_MODULE_1__.RefreshCwIcon, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Tekrar Dene\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\abstract-essay\\\\frontend\\\\app\\\\components\\\\ErrorMessage.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ErrorMessage.tsx\n");

/***/ })

};
;