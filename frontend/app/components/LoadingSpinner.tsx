'use client'

import { Loader2Icon, BrainIcon, FileTextIcon, BarChart3Icon } from './SimpleIcons'

export default function LoadingSpinner() {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="relative">
        {/* Main spinner */}
        <div className="h-12 w-12 text-indigo-600">
          <Loader2Icon />
        </div>

        {/* Floating icons */}
        <div className="absolute -top-8 -left-8 animate-bounce delay-100">
          <div className="h-6 w-6 text-purple-500">
            <BrainIcon />
          </div>
        </div>
        <div className="absolute -top-8 -right-8 animate-bounce delay-300">
          <div className="h-6 w-6 text-blue-500">
            <FileTextIcon />
          </div>
        </div>
        <div className="absolute -bottom-8 -left-8 animate-bounce delay-500">
          <div className="h-6 w-6 text-green-500">
            <BarChart3Icon />
          </div>
        </div>
      </div>
      
      <div className="mt-8 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Makale Analiz Ediliyor
        </h3>
        <p className="text-gray-600 max-w-md">
          Yapay zekamız makale verilerini işliyor, temel bulgular çıkarıyor
          ve kapsamlı bir analiz hazırlıyor...
        </p>
        
        {/* Progress steps */}
        <div className="mt-6 space-y-2">
          <div className="flex items-center justify-center space-x-2 text-sm">
            <div className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse"></div>
            <span className="text-gray-600">Makale verileri çekiliyor</span>
          </div>
          <div className="flex items-center justify-center space-x-2 text-sm">
            <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse delay-200"></div>
            <span className="text-gray-600">AI analizi devam ediyor</span>
          </div>
          <div className="flex items-center justify-center space-x-2 text-sm">
            <div className="w-2 h-2 bg-green-600 rounded-full animate-pulse delay-400"></div>
            <span className="text-gray-600">Görselleştirme oluşturuluyor</span>
          </div>
        </div>
      </div>
    </div>
  )
}
