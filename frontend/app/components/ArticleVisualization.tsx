'use client'

import { useState } from 'react'
import { DownloadIcon, EyeIcon, CodeIcon } from './SimpleIcons'

interface Analysis {
  title: string
  authors: string
  journal: string
  year: string
  field: string
  abstract: string
  findings: string
  conclusion: string
  methodology: string
  methods: string
  dataQuality: string
  limitations: string
  keywords: string
  citationCount: number
  pmid: string
}

interface ArticleVisualizationProps {
  analysis: Analysis
}

export default function ArticleVisualization({ analysis }: ArticleVisualizationProps) {
  const [activeTab, setActiveTab] = useState<'visual' | 'data'>('visual')

  // Alan bazlı renk seçimi
  const getFieldColor = (field: string) => {
    const colors = {
      'Tıp': 'from-red-500 to-red-600',
      'Mühendislik': 'from-orange-500 to-orange-600',
      'Fizik': 'from-purple-500 to-purple-600',
      'Kimya': 'from-teal-500 to-teal-600',
      'Bilgisayar': 'from-blue-500 to-blue-600',
      'Sosyal Bilimler': 'from-green-500 to-green-600',
      'Matematik': 'from-indigo-500 to-indigo-600',
      'Biyoloji': 'from-emerald-500 to-emerald-600',
      'Genel': 'from-gray-500 to-gray-600'
    }
    return colors[field as keyof typeof colors] || colors['Genel']
  }

  // Metin sarma fonksiyonu
  const wrapText = (text: string, maxLength: number): string[] => {
    if (!text) return [''];

    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      if ((currentLine + word).length <= maxLength) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          lines.push(word.slice(0, maxLength));
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  };

  // HTML entities'leri temizle ve Türkçe karakterleri koru
  const cleanText = (text: string): string => {
    if (!text) return '';

    return text
      .replace(/<[^>]*>/g, '') // HTML taglarını kaldır
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      // Türkçe karakterleri koru
      .replace(/&ccedil;/g, 'ç')
      .replace(/&Ccedil;/g, 'Ç')
      .replace(/&#231;/g, 'ç')
      .replace(/&#199;/g, 'Ç')
      .replace(/&gbreve;/g, 'ğ')
      .replace(/&Gbreve;/g, 'Ğ')
      .replace(/&#287;/g, 'ğ')
      .replace(/&#286;/g, 'Ğ')
      .replace(/&idot;/g, 'ı')
      .replace(/&#305;/g, 'ı')
      .replace(/&Idot;/g, 'İ')
      .replace(/&#304;/g, 'İ')
      .replace(/&ouml;/g, 'ö')
      .replace(/&Ouml;/g, 'Ö')
      .replace(/&#246;/g, 'ö')
      .replace(/&#214;/g, 'Ö')
      .replace(/&scedil;/g, 'ş')
      .replace(/&Scedil;/g, 'Ş')
      .replace(/&#351;/g, 'ş')
      .replace(/&#350;/g, 'Ş')
      .replace(/&uuml;/g, 'ü')
      .replace(/&Uuml;/g, 'Ü')
      .replace(/&#252;/g, 'ü')
      .replace(/&#220;/g, 'Ü')
      .trim();
  };

  const generateSVG = () => {
    const fieldColor = analysis.field === 'Tıp' ? '#dc3545' :
                      analysis.field === 'Mühendislik' ? '#fd7e14' :
                      analysis.field === 'Fizik' ? '#6f42c1' :
                      analysis.field === 'Kimya' ? '#20c997' :
                      analysis.field === 'Bilgisayar' ? '#0d6efd' :
                      analysis.field === 'Sosyal Bilimler' ? '#198754' :
                      '#3d6a80'

    const qualityColor = analysis.dataQuality === 'FULL' ? '#198754' :
                        analysis.dataQuality === 'PARTIAL' ? '#fd7e14' : '#dc3545'

    // Metinleri temizle ve sarma
    const cleanTitle = cleanText(analysis.title);
    const titleLines = wrapText(cleanTitle, 70);

    const cleanAbstract = cleanText(analysis.abstract);
    const abstractLines = wrapText(cleanAbstract, 80);

    const cleanFindings = cleanText(analysis.findings.replace(/\n/g, ' '));
    const findingsLines = wrapText(cleanFindings, 120);

    const cleanMethodology = cleanText(analysis.methodology);
    const methodologyLines = wrapText(cleanMethodology, 50);

    const cleanConclusion = cleanText(analysis.conclusion);
    const conclusionLines = wrapText(cleanConclusion, 60);

    const cleanAuthors = cleanText(analysis.authors);
    const authorLines = wrapText(cleanAuthors, 50);

    const cleanJournal = cleanText(analysis.journal);
    const cleanKeywords = cleanText(analysis.keywords);

    // SVG metinlerini XML-safe hale getir
    const xmlSafe = (text: string): string => {
      return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
    };

    return `<?xml version="1.0" encoding="UTF-8"?>
      <svg xmlns="http://www.w3.org/2000/svg" width="1000" height="800" viewBox="0 0 1000 800">
        <defs>
          <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
          </linearGradient>
          <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:${fieldColor};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${fieldColor}dd;stop-opacity:1" />
          </linearGradient>
        </defs>

        <!-- Background -->
        <rect width="1000" height="800" fill="url(#bgGradient)"/>

        <!-- Header -->
        <rect width="1000" height="100" fill="url(#headerGradient)"/>
        ${titleLines.slice(0, 2).map((line, index) =>
          `<text x="20" y="${30 + index * 25}" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">${xmlSafe(line)}</text>`
        ).join('\n        ')}
        <text x="20" y="85" font-family="Arial, sans-serif" font-size="14" fill="white" opacity="0.9">
          📚 ${xmlSafe(analysis.field)}
        </text>

        <!-- Data Quality -->
        <rect x="20" y="120" width="300" height="40" fill="${qualityColor}" rx="5"/>
        <text x="30" y="145" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
          Veri Kalitesi: ${xmlSafe(analysis.dataQuality)}
        </text>

        <!-- Article Info -->
        <rect x="20" y="180" width="460" height="160" fill="#e9ecef" rx="5"/>
        <text x="30" y="205" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#212529">
          Makale Bilgileri
        </text>
        <text x="30" y="230" font-family="Arial, sans-serif" font-size="14" fill="#212529">
          Yazarlar:
        </text>
        ${authorLines.slice(0, 2).map((line, index) =>
          `<text x="30" y="${250 + index * 18}" font-family="Arial, sans-serif" font-size="12" fill="#212529">${xmlSafe(line)}</text>`
        ).join('\n        ')}
        <text x="30" y="290" font-family="Arial, sans-serif" font-size="14" fill="#212529">
          Dergi: ${xmlSafe(cleanJournal.slice(0, 40))}${cleanJournal.length > 40 ? '...' : ''} (${xmlSafe(analysis.year)})
        </text>
        ${cleanKeywords ? `<text x="30" y="315" font-family="Arial, sans-serif" font-size="12" fill="#6c757d" font-style="italic">
          Anahtar Kelimeler: ${xmlSafe(cleanKeywords.slice(0, 50))}${cleanKeywords.length > 50 ? '...' : ''}
        </text>` : ''}

        <!-- Abstract -->
        <rect x="500" y="180" width="480" height="160" fill="#d1e7dd" rx="5"/>
        <text x="510" y="205" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#0f5132">
          Özet
        </text>
        ${abstractLines.slice(0, 6).map((line, index) =>
          `<text x="510" y="${230 + index * 18}" font-family="Arial, sans-serif" font-size="12" fill="#0f5132">${xmlSafe(line)}</text>`
        ).join('\n        ')}

        <!-- Findings -->
        <rect x="20" y="360" width="960" height="140" fill="#cfe2ff" rx="5"/>
        <text x="30" y="385" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#084298">
          Ana Bulgular
        </text>
        ${findingsLines.slice(0, 5).map((line, index) =>
          `<text x="30" y="${410 + index * 18}" font-family="Arial, sans-serif" font-size="12" fill="#084298">• ${xmlSafe(line)}</text>`
        ).join('\n        ')}

        <!-- Methodology -->
        <rect x="20" y="520" width="460" height="120" fill="#fff3cd" rx="5"/>
        <text x="30" y="545" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#664d03">
          Metodoloji
        </text>
        ${methodologyLines.slice(0, 4).map((line, index) =>
          `<text x="30" y="${570 + index * 18}" font-family="Arial, sans-serif" font-size="12" fill="#664d03">${xmlSafe(line)}</text>`
        ).join('\n        ')}

        <!-- Conclusion -->
        <rect x="500" y="520" width="480" height="120" fill="#f8d7da" rx="5"/>
        <text x="510" y="545" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#842029">
          Sonuç
        </text>
        ${conclusionLines.slice(0, 4).map((line, index) =>
          `<text x="510" y="${570 + index * 18}" font-family="Arial, sans-serif" font-size="12" fill="#842029">${xmlSafe(line)}</text>`
        ).join('\n        ')}

        <!-- Footer -->
        <text x="20" y="760" font-family="Arial, sans-serif" font-size="12" fill="#6c757d" font-style="italic">
          Bu özet yapay zeka tarafından oluşturulmuştur. Detaylı bilgi için orijinal makaleye başvurunuz.
        </text>
      </svg>`
  }

  const downloadSVG = () => {
    const svgContent = generateSVG()
    const blob = new Blob([svgContent], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `article-summary-${analysis.field.toLowerCase()}.svg`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          <button
            onClick={() => setActiveTab('visual')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'visual'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <EyeIcon />
            <span className="ml-2">Görsel Özet</span>
          </button>
          <button
            onClick={() => setActiveTab('data')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'data'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <CodeIcon />
            <span className="ml-2">Detaylı Veriler</span>
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'visual' && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">
                Makale Özet Görselleştirmesi
              </h3>
              <button
                onClick={downloadSVG}
                className="inline-flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
              >
                <DownloadIcon />
                <span>SVG İndir</span>
              </button>
            </div>

            {/* SVG Preview */}
            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div 
                className="w-full overflow-auto"
                dangerouslySetInnerHTML={{ __html: generateSVG() }}
              />
            </div>
          </div>
        )}

        {activeTab === 'data' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">
              Detaylı Analiz Verileri
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Abstract */}
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">Özet</h4>
                <div className="max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-green-300 scrollbar-track-green-100">
                  <p className="text-green-800 text-sm pr-2">{analysis.abstract}</p>
                </div>
              </div>

              {/* Methodology */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Metodoloji</h4>
                <div className="max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-blue-100">
                  <p className="text-blue-800 text-sm pr-2">{analysis.methodology}</p>
                </div>
              </div>

              {/* Methods */}
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">Yöntemler</h4>
                <div className="max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-300 scrollbar-track-purple-100">
                  <p className="text-purple-800 text-sm pr-2">{analysis.methods}</p>
                </div>
              </div>

              {/* Limitations */}
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">Sınırlamalar</h4>
                <div className="max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-yellow-300 scrollbar-track-yellow-100">
                  <p className="text-yellow-800 text-sm pr-2">{analysis.limitations}</p>
                </div>
              </div>
            </div>

            {/* Findings */}
            <div className="bg-indigo-50 p-4 rounded-lg">
              <h4 className="font-medium text-indigo-900 mb-2">Ana Bulgular</h4>
              <div className="max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-indigo-300 scrollbar-track-indigo-100">
                <div className="text-indigo-800 text-sm whitespace-pre-line pr-2">
                  {analysis.findings}
                </div>
              </div>
            </div>

            {/* Conclusion */}
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-medium text-red-900 mb-2">Sonuç</h4>
              <div className="max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-red-300 scrollbar-track-red-100">
                <p className="text-red-800 text-sm pr-2">{analysis.conclusion}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
