'use client'

import { AlertTriangleIcon, RefreshCwIcon } from './SimpleIcons'

interface ErrorMessageProps {
  message: string
  onRetry?: () => void
}

export default function ErrorMessage({ message, onRetry }: ErrorMessageProps) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-6">
      <div className="flex items-start space-x-3">
        <div className="h-6 w-6 text-red-600 flex-shrink-0 mt-0.5">
          <AlertTriangleIcon />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-medium text-red-900 mb-2">
            Ana<PERSON><PERSON>arısız
          </h3>
          <p className="text-red-700 mb-4">
            {message}
          </p>
          
          <div className="space-y-2 text-sm text-red-600">
            <p><strong>Olası çözümler:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>DOI numarasının doğru olduğunu kontrol edin</li>
              <li>Büyük bir yayıncıdan farklı bir DOI deneyin</li>
              <li>Bir süre bekleyip tekrar deneyin</li>
              <li>İnternet bağlantınızı kontrol edin</li>
            </ul>
          </div>
          
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-4 inline-flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
            >
              <RefreshCwIcon />
              <span>Tekrar Dene</span>
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
