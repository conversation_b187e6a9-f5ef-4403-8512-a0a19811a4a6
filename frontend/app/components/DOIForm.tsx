'use client'

import { useState } from 'react'
import { SearchIcon, Loader2Icon } from './SimpleIcons'

interface DOIFormProps {
  onSubmit: (doi: string) => void
  isLoading: boolean
}

export default function DOIForm({ onSubmit, isLoading }: DOIFormProps) {
  const [doi, setDoi] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (doi.trim() && !isLoading) {
      onSubmit(doi.trim())
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex space-x-3">
        <div className="flex-1">
          <label htmlFor="doi" className="sr-only">
            DOI Number
          </label>
          <input
            type="text"
            id="doi"
            value={doi}
            onChange={(e) => setDoi(e.target.value)}
            placeholder="DOI numarası girin (örn: 10.1016/j.kint.2021.02.040)"
            className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 placeholder-gray-500 bg-white"
            disabled={isLoading}
          />
        </div>
        <button
          type="submit"
          disabled={!doi.trim() || isLoading}
          className="px-6 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
        >
          {isLoading ? (
            <Loader2Icon />
          ) : (
            <SearchIcon />
          )}
          <span>{isLoading ? 'Analyzing...' : 'Analyze'}</span>
        </button>
      </div>
      
      <p className="text-sm text-gray-500">
        Akademik makaleyi analiz etmek için DOI numarası girin. Sistem tüm akademik alanlardan makaleleri destekler.
      </p>
    </form>
  )
}
