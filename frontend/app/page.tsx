'use client'

import { useState } from 'react'
import dynamic from 'next/dynamic'
import {
  SearchIcon,
  FileTextIcon,
  DownloadIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  ClockIcon
} from './components/SimpleIcons'

// Dinamik import ile bileşenleri yükle
const DOIForm = dynamic(() => import('./components/DOIForm'), { ssr: false })
const LoadingSpinner = dynamic(() => import('./components/LoadingSpinner'), { ssr: false })
const ArticleVisualization = dynamic(() => import('./components/ArticleVisualization'), { ssr: false })
const ErrorMessage = dynamic(() => import('./components/ErrorMessage'), { ssr: false })

interface AnalysisResult {
  doi: string
  analysis: {
    title: string
    authors: string
    journal: string
    year: string
    field: string
    abstract: string
    findings: string
    conclusion: string
    methodology: string
    methods: string
    dataQuality: string
    limitations: string
    keywords: string
    citationCount: number
    pmid: string
  }
  pdf_access: {
    is_open_access: boolean
    pdf_url?: string
    best_pdf?: {
      url: string
      host_type?: string
      license?: string
    }
    all_pdf_urls?: Array<{
      url: string
      host_type: string
      license?: string
      repository_institution?: string
    }>
    oa_date?: string
    journal_is_oa?: boolean
    journal_is_in_doaj?: boolean
    publisher?: string
    journal_name?: string
    has_repository_copy?: boolean
    oa_locations_count?: number
    error?: string
  }
  processed_at: string
}

export default function Home() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<AnalysisResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleDOISubmit = async (doi: string) => {
    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch(`/api/summary?doi=${encodeURIComponent(doi)}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const data: AnalysisResult = await response.json()
      setResult(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bilinmeyen hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-3">
            <FileTextIcon />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Akademik Makale Analiz Sistemi
              </h1>
              <p className="text-gray-600 mt-1">
                Yapay zeka destekli akademik makale analizi ve görselleştirme platformu
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* DOI Input Form */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex items-center space-x-2 mb-4">
            <SearchIcon />
            <h2 className="text-xl font-semibold text-gray-900">
              Analiz Edilecek DOI'yi Girin
            </h2>
          </div>
          
          <DOIForm onSubmit={handleDOISubmit} isLoading={isLoading} />
          
          {/* Example DOIs */}
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Örnek DOI Numaraları (farklı alanlardan):
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {[
                { doi: '10.1016/j.kint.2021.02.040', field: 'Tıp', desc: 'Böbrek araştırması' },
                { doi: '10.1038/s41586-021-03819-2', field: 'Fizik', desc: 'Kuantum bilişim' },
                { doi: '10.1145/3447548.3467401', field: 'Bilgisayar', desc: 'Makine öğrenmesi' },
                { doi: '10.1007/s44163-022-00022-8', field: 'Sosyal Bilimler', desc: 'Problem çözme' },
              ].map((example, index) => (
                <button
                  key={index}
                  onClick={() => handleDOISubmit(example.doi)}
                  className="text-left p-3 rounded-md border border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 transition-colors"
                  disabled={isLoading}
                >
                  <div className="font-mono text-sm text-indigo-600">{example.doi}</div>
                  <div className="text-xs text-gray-500">{example.field}: {example.desc}</div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="bg-white rounded-lg shadow-md p-8">
            <LoadingSpinner />
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="mb-8">
            <ErrorMessage message={error} />
          </div>
        )}

        {/* Results */}
        {result && !isLoading && (
          <div className="space-y-8">
            {/* Article Info Card */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon />
                  <h2 className="text-xl font-semibold text-gray-900">
                    Analiz Tamamlandı
                  </h2>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <ClockIcon />
                  <span>{new Date(result.processed_at).toLocaleString('tr-TR')}</span>
                </div>
              </div>

              {/* Article Basic Info */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <div className="lg:col-span-2">
                  <div className="max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 mb-4">
                    <h3 className="text-lg font-medium text-gray-900 mb-2 pr-2">
                      {result.analysis.title}
                    </h3>
                  </div>
                  <div className="max-h-20 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 mb-2">
                    <p className="text-gray-600 pr-2">
                      <strong>Yazarlar:</strong> {result.analysis.authors}
                    </p>
                  </div>
                  <p className="text-gray-600 mb-2">
                    <strong>Dergi:</strong> {result.analysis.journal} ({result.analysis.year})
                  </p>
                  <p className="text-gray-600">
                    <strong>Alan:</strong>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                      result.analysis.field === 'Tıp' ? 'bg-red-100 text-red-800' :
                      result.analysis.field === 'Mühendislik' ? 'bg-orange-100 text-orange-800' :
                      result.analysis.field === 'Fizik' ? 'bg-purple-100 text-purple-800' :
                      result.analysis.field === 'Kimya' ? 'bg-teal-100 text-teal-800' :
                      result.analysis.field === 'Bilgisayar' ? 'bg-blue-100 text-blue-800' :
                      result.analysis.field === 'Sosyal Bilimler' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {result.analysis.field}
                    </span>
                  </p>
                </div>

                <div className="space-y-3">
                  {/* Data Quality Indicator */}
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">Veri Kalitesi:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      result.analysis.dataQuality === 'FULL' ? 'bg-green-100 text-green-800' :
                      result.analysis.dataQuality === 'PARTIAL' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {result.analysis.dataQuality}
                    </span>
                  </div>

                  {/* PDF Access - Gelişmiş */}
                  {result.pdf_access.is_open_access && (
                    <div className="space-y-3">
                      {/* Ana PDF Linki */}
                      {result.pdf_access.pdf_url && (
                        <a
                          href={result.pdf_access.pdf_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                        >
                          <DownloadIcon />
                          <span>PDF İndir (Ana)</span>
                        </a>
                      )}

                      {/* Ek PDF Linkleri */}
                      {result.pdf_access.all_pdf_urls && result.pdf_access.all_pdf_urls.length > 1 && (
                        <details className="text-sm">
                          <summary className="cursor-pointer text-green-600 hover:text-green-700">
                            +{result.pdf_access.all_pdf_urls.length - 1} ek PDF kaynağı
                          </summary>
                          <div className="mt-2 space-y-1">
                            {result.pdf_access.all_pdf_urls.slice(1).map((pdf, index) => (
                              <a
                                key={index}
                                href={pdf.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="block text-blue-600 hover:text-blue-700 text-xs"
                              >
                                {pdf.host_type} {pdf.repository_institution && `(${pdf.repository_institution})`}
                              </a>
                            ))}
                          </div>
                        </details>
                      )}

                      {/* OA Bilgileri */}
                      <div className="text-xs text-gray-600 space-y-1">
                        {result.pdf_access.journal_is_oa && (
                          <div className="text-green-600">✓ Açık Erişim Dergisi</div>
                        )}
                        {result.pdf_access.journal_is_in_doaj && (
                          <div className="text-green-600">✓ DOAJ'da Listelendi</div>
                        )}
                        {result.pdf_access.has_repository_copy && (
                          <div className="text-blue-600">📚 Repository Kopyası Mevcut</div>
                        )}
                        {result.pdf_access.oa_date && (
                          <div>Açık Erişim Tarihi: {result.pdf_access.oa_date}</div>
                        )}
                      </div>
                    </div>
                  )}

                  {result.pdf_access.is_open_access === false && (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <AlertCircleIcon />
                        <span>PDF ücretsiz erişime açık değil</span>
                      </div>
                      {result.pdf_access.publisher && (
                        <div className="text-xs text-gray-400">
                          Yayıncı: {result.pdf_access.publisher}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Visualization */}
            <ArticleVisualization analysis={result.analysis} />
          </div>
        )}
      </main>
    </div>
  )
}
