@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Custom Scrollbar Styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thumb-green-300::-webkit-scrollbar-thumb {
    background-color: #86efac;
    border-radius: 3px;
  }

  .scrollbar-track-green-100::-webkit-scrollbar-track {
    background-color: #dcfce7;
    border-radius: 3px;
  }

  .scrollbar-thumb-blue-300::-webkit-scrollbar-thumb {
    background-color: #93c5fd;
    border-radius: 3px;
  }

  .scrollbar-track-blue-100::-webkit-scrollbar-track {
    background-color: #dbeafe;
    border-radius: 3px;
  }

  .scrollbar-thumb-purple-300::-webkit-scrollbar-thumb {
    background-color: #c4b5fd;
    border-radius: 3px;
  }

  .scrollbar-track-purple-100::-webkit-scrollbar-track {
    background-color: #ede9fe;
    border-radius: 3px;
  }

  .scrollbar-thumb-yellow-300::-webkit-scrollbar-thumb {
    background-color: #fde047;
    border-radius: 3px;
  }

  .scrollbar-track-yellow-100::-webkit-scrollbar-track {
    background-color: #fefce8;
    border-radius: 3px;
  }

  .scrollbar-thumb-indigo-300::-webkit-scrollbar-thumb {
    background-color: #a5b4fc;
    border-radius: 3px;
  }

  .scrollbar-track-indigo-100::-webkit-scrollbar-track {
    background-color: #e0e7ff;
    border-radius: 3px;
  }

  .scrollbar-thumb-red-300::-webkit-scrollbar-thumb {
    background-color: #fca5a5;
    border-radius: 3px;
  }

  .scrollbar-track-red-100::-webkit-scrollbar-track {
    background-color: #fee2e2;
    border-radius: 3px;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }

  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background-color: #f3f4f6;
    border-radius: 3px;
  }

  /* Hover effects for scrollbars */
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    opacity: 0.8;
  }

  /* Firefox scrollbar support */
  .scrollbar-thin {
    scrollbar-color: #d1d5db #f3f4f6;
  }
}
