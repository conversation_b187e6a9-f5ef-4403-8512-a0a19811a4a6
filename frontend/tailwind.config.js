/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'field-medicine': '#dc3545',
        'field-engineering': '#fd7e14',
        'field-physics': '#6f42c1',
        'field-chemistry': '#20c997',
        'field-computer': '#0d6efd',
        'field-social': '#198754',
        'field-math': '#6610f2',
        'field-biology': '#198754',
        'field-general': '#3d6a80',
      },
    },
  },
  plugins: [],
}
