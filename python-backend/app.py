#!/usr/bin/env python3

import json
import subprocess
import sys
import time
import urllib.request
import urllib.parse
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging

# Flask app setup
app = Flask(__name__)
CORS(app)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UnpaywallService:
    @staticmethod
    def get_pdf_link(doi, email='<EMAIL>'):
        """Unpaywall API ile PDF link alma"""
        try:
            url = f'https://api.unpaywall.org/v2/{doi}?email={email}'

            with urllib.request.urlopen(url, timeout=10) as response:
                if response.status == 200:
                    data = json.loads(response.read().decode())

                    if data.get('is_oa') and data.get('best_oa_location'):
                        return {
                            'is_open_access': True,
                            'pdf_url': data['best_oa_location'].get('url_for_pdf'),
                            'host_type': data['best_oa_location'].get('host_type'),
                            'license': data['best_oa_location'].get('license')
                        }
                    else:
                        return {'is_open_access': False, 'pdf_url': None}
                else:
                    return {'is_open_access': False, 'pdf_url': None, 'error': f'Unpaywall API error: {response.status}'}

        except Exception as e:
            logger.error(f'Unpaywall API error: {e}')
            return {'is_open_access': False, 'pdf_url': None, 'error': str(e)}

class CLIAgentService:
    @staticmethod
    def analyze_doi(doi):
        """CLI Agent ile DOI analizi"""
        try:
            logger.info(f'Analyzing DOI: {doi}')
            
            # CLI agent'ı çalıştır
            result = subprocess.run(
                ['node', '../cli-agent.js', doi],
                capture_output=True,
                text=True,
                timeout=30,
                cwd='.'
            )
            
            if result.returncode == 0:
                # JSON parse et
                try:
                    return json.loads(result.stdout)
                except json.JSONDecodeError as e:
                    logger.error(f'JSON parse error: {e}')
                    logger.error(f'Raw output: {result.stdout}')
                    return CLIAgentService.create_fallback_analysis(doi)
            else:
                logger.error(f'CLI agent failed: {result.stderr}')
                return CLIAgentService.create_fallback_analysis(doi)
                
        except subprocess.TimeoutExpired:
            logger.error('CLI agent timeout')
            return CLIAgentService.create_fallback_analysis(doi)
        except Exception as e:
            logger.error(f'CLI agent error: {e}')
            return CLIAgentService.create_fallback_analysis(doi)
    
    @staticmethod
    def create_fallback_analysis(doi):
        """Fallback analiz"""
        return {
            'title': 'Makale analizi yapılamadı',
            'authors': 'Bilinmiyor',
            'journal': 'Bilinmiyor',
            'year': 'Bilinmiyor',
            'field': 'Genel',
            'abstract': 'Özet alınamadı',
            'findings': '• Analiz yapılamadı\n• Lütfen daha sonra tekrar deneyin',
            'conclusion': 'Sonuç analiz edilemedi',
            'methodology': 'Metodoloji bilgisi alınamadı',
            'methods': 'Yöntem bilgisi alınamadı',
            'dataQuality': 'LIMITED',
            'limitations': 'Sistem hatası nedeniyle analiz tamamlanamadı',
            'keywords': '',
            'citationCount': 0,
            'pmid': '',
            'error': 'CLI agent analizi başarısız'
        }

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'OK',
        'timestamp': time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime())
    })

@app.route('/summary', methods=['GET'])
def get_summary():
    """Ana endpoint - DOI analizi"""
    doi = request.args.get('doi')
    
    if not doi or not doi.strip():
        return jsonify({'error': 'DOI parametresi gerekli'}), 400
    
    # DOI'yi temizle
    clean_doi = doi.replace('https://doi.org/', '').replace('http://dx.doi.org/', '')
    
    logger.info(f'Processing DOI: {clean_doi}')
    
    try:
        # Paralel olarak analiz ve PDF kontrolü yap
        import threading
        
        analysis_result = [None]
        unpaywall_result = [None]
        
        def run_analysis():
            analysis_result[0] = CLIAgentService.analyze_doi(clean_doi)
        
        def run_unpaywall():
            unpaywall_result[0] = UnpaywallService.get_pdf_link(clean_doi)
        
        # Thread'leri başlat
        analysis_thread = threading.Thread(target=run_analysis)
        unpaywall_thread = threading.Thread(target=run_unpaywall)
        
        analysis_thread.start()
        unpaywall_thread.start()
        
        # Thread'leri bekle
        analysis_thread.join(timeout=30)
        unpaywall_thread.join(timeout=10)
        
        # Sonuçları birleştir
        result = {
            'doi': clean_doi,
            'analysis': analysis_result[0] or CLIAgentService.create_fallback_analysis(clean_doi),
            'pdf_access': unpaywall_result[0] or {'is_open_access': False, 'pdf_url': None},
            'processed_at': time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime())
        }
        
        logger.info(f'Analysis completed for DOI: {clean_doi}')
        return jsonify(result)
        
    except Exception as e:
        logger.error(f'Error processing DOI {clean_doi}: {e}')
        return jsonify({
            'error': 'Makale analizi sırasında hata oluştu',
            'details': str(e),
            'doi': clean_doi
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint bulunamadı'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Sunucu hatası', 'details': str(error)}), 500

if __name__ == '__main__':
    print("🚀 Python Flask Backend starting...")
    print("📡 API Endpoint: http://localhost:4567/summary?doi=...")
    print("🔍 Health Check: http://localhost:4567/health")
    print("📚 Example: http://localhost:4567/summary?doi=10.1016/j.kint.2021.02.040")
    
    app.run(host='0.0.0.0', port=4567, debug=True)
