// Gelişmiş test - Birden fazla API kaynağı ile
import { lookupDOI } from '@uwdata/citation-query';
import fs from 'fs';

// Crossref API ile ek veri çekme
async function fetchCrossrefData(doi) {
  try {
    console.log('📡 Crossref API çağrılıyor...');
    const response = await fetch(`https://api.crossref.org/works/${doi}`, {
      headers: {
        'User-Agent': 'DOI-Summary-Tool/1.0 (mailto:<EMAIL>)'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Crossref API error: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('✅ Crossref verisi alındı');
    return data.message;
  } catch (error) {
    console.warn('⚠️ Crossref API başarısız:', error.message);
    return null;
  }
}

// Semantic Scholar API ile ek veri çekme
async function fetchSemanticScholarData(doi) {
  try {
    console.log('📡 Semantic Scholar API çağrılıyor...');
    const response = await fetch(
      `https://api.semanticscholar.org/graph/v1/paper/DOI:${doi}?fields=title,abstract,authors,year,journal,citationCount,influentialCitationCount,fieldsOfStudy,references,citations`
    );
    
    if (!response.ok) {
      console.warn(`⚠️ Semantic Scholar API error: ${response.status}`);
      return null;
    }
    
    const data = await response.json();
    console.log('✅ Semantic Scholar verisi alındı');
    return data;
  } catch (error) {
    console.warn('⚠️ Semantic Scholar API başarısız:', error.message);
    return null;
  }
}

// PubMed API ile ek veri çekme
async function fetchPubMedData(doi) {
  try {
    console.log('📡 PubMed API çağrılıyor...');
    
    // DOI ile PubMed ID bulma
    const searchResponse = await fetch(
      `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=${encodeURIComponent(doi)}&retmode=json`
    );
    
    if (!searchResponse.ok) return null;
    
    const searchData = await searchResponse.json();
    const pmid = searchData.esearchresult?.idlist?.[0];
    
    if (!pmid) {
      console.warn('⚠️ PubMed ID bulunamadı');
      return null;
    }
    
    console.log(`📋 PubMed ID bulundu: ${pmid}`);
    
    // PubMed detayları çekme
    const detailResponse = await fetch(
      `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=${pmid}&retmode=xml`
    );
    
    if (!detailResponse.ok) return null;
    
    const xmlText = await detailResponse.text();
    
    // Basit XML parsing
    const abstractMatch = xmlText.match(/<AbstractText[^>]*>(.*?)<\/AbstractText>/s);
    const abstract = abstractMatch ? abstractMatch[1].replace(/<[^>]*>/g, '').trim() : null;
    
    const keywordsMatch = xmlText.match(/<Keyword[^>]*>(.*?)<\/Keyword>/g);
    const keywords = keywordsMatch ? keywordsMatch.map(k => k.replace(/<[^>]*>/g, '').trim()).join(', ') : null;
    
    console.log('✅ PubMed verisi alındı');
    return { abstract, keywords, pmid };
  } catch (error) {
    console.warn('⚠️ PubMed API başarısız:', error.message);
    return null;
  }
}

// Akıllı analiz fonksiyonu
function analyzeArticleData(primaryData, crossrefData, semanticData, pubmedData) {
  console.log('🧠 Makale verisi analiz ediliyor...');
  
  // En iyi veriyi birleştir
  const title = semanticData?.title || 
               crossrefData?.title?.[0] || 
               primaryData?.title?.[0] || 
               primaryData?.title || 
               'Başlık bulunamadı';
  
  // Yazarları formatla
  let authors = 'Yazar bilgisi bulunamadı';
  if (semanticData?.authors) {
    authors = semanticData.authors.map(a => a.name).join(', ');
  } else if (primaryData?.author) {
    authors = primaryData.author.map(a => `${a.given || ''} ${a.family || ''}`.trim())
                        .filter(name => name.length > 0).join(', ');
  } else if (crossrefData?.author) {
    authors = crossrefData.author.map(a => `${a.given || ''} ${a.family || ''}`.trim())
                            .filter(name => name.length > 0).join(', ');
  }
  
  // Dergi adını al
  const journal = semanticData?.journal?.name ||
                 crossrefData?.['container-title']?.[0] ||
                 primaryData?.['container-title']?.[0] || 
                 primaryData?.['container-title'] || 
                 primaryData?.journal || 
                 'Dergi bilgisi bulunamadı';
  
  // Yayın yılını al
  const year = semanticData?.year?.toString() ||
               crossrefData?.issued?.['date-parts']?.[0]?.[0]?.toString() ||
               primaryData?.issued?.['date-parts']?.[0]?.[0]?.toString() || 
               'Yıl bilgisi bulunamadı';
  
  // Abstract - en detaylısını al
  const abstract = pubmedData?.abstract ||
                  semanticData?.abstract ||
                  crossrefData?.abstract ||
                  primaryData?.abstract ||
                  'Özet mevcut değil';
  
  // Anahtar kelimeleri al
  const keywords = pubmedData?.keywords ||
                  semanticData?.fieldsOfStudy?.join(', ') ||
                  primaryData?.subject?.join(', ') || 
                  '';
  
  // Veri kalitesini değerlendir
  let dataQuality = 'LIMITED';
  if (abstract !== 'Özet mevcut değil' && keywords && authors !== 'Yazar bilgisi bulunamadı') {
    dataQuality = 'FULL';
  } else if (abstract !== 'Özet mevcut değil' || keywords) {
    dataQuality = 'PARTIAL';
  }
  
  // Akıllı tahminler yap
  const findings = generateFindings(title, abstract, keywords, journal);
  const methodology = generateMethodology(title, abstract, journal);
  const conclusion = generateConclusion(title, abstract);
  const statistics = generateStatistics(journal, abstract);
  
  return {
    title,
    authors,
    journal,
    year,
    abstract,
    keywords,
    findings,
    methodology,
    conclusion,
    statistics,
    dataQuality,
    citationCount: semanticData?.citationCount || 0,
    fieldsOfStudy: semanticData?.fieldsOfStudy?.join(', ') || '',
    pmid: pubmedData?.pmid || '',
    limitations: generateLimitations(dataQuality, abstract)
  };
}

function generateFindings(title, abstract, keywords, journal) {
  const findings = [];
  
  if (abstract && abstract !== 'Özet mevcut değil') {
    // Abstract'tan bulgular çıkar
    if (abstract.includes('significant') || abstract.includes('anlamlı')) {
      findings.push('İstatistiksel olarak anlamlı sonuçlar elde edildi');
    }
    if (abstract.includes('randomized') || abstract.includes('randomize')) {
      findings.push('Randomize kontrollü çalışma tasarımı kullanıldı');
    }
    if (abstract.includes('efficacy') || abstract.includes('etkinlik')) {
      findings.push('Tedavi etkinliği değerlendirildi');
    }
  }
  
  // Başlıktan çıkarımlar
  if (title.includes('multicenter') || title.includes('çok merkezli')) {
    findings.push('Çok merkezli çalışma gerçekleştirildi');
  }
  
  // Dergi türüne göre tahminler
  if (journal.includes('Kidney') || journal.includes('Nephrol')) {
    findings.push('Böbrek hastalıkları alanında bulgular elde edildi');
  }
  
  if (findings.length === 0) {
    findings.push('Mevcut verilerle spesifik bulgular belirlenemedi');
    findings.push('Detaylı analiz için tam metne erişim gerekli');
  }
  
  return findings.map(f => `• ${f}`).join('\n');
}

function generateMethodology(title, abstract, journal) {
  if (abstract && abstract !== 'Özet mevcut değil') {
    if (abstract.includes('randomized controlled trial')) {
      return 'Randomize kontrollü çalışma metodolojisi kullanıldı. Hastalar rastgele gruplara ayrıldı.';
    }
    if (abstract.includes('retrospective')) {
      return 'Retrospektif çalışma tasarımı kullanıldı. Geçmiş veriler analiz edildi.';
    }
    if (abstract.includes('prospective')) {
      return 'Prospektif çalışma tasarımı kullanıldı. İleriye dönük veri toplama yapıldı.';
    }
  }
  
  if (title.includes('trial')) {
    return 'Klinik çalışma metodolojisi uygulandı. Kontrollü deney tasarımı kullanıldı.';
  }
  
  return 'Metodoloji bilgisi mevcut verilerle belirlenemedi. Tam metin incelemesi gerekli.';
}

function generateConclusion(title, abstract) {
  if (abstract && abstract !== 'Özet mevcut değil') {
    // Abstract'ın son kısmından sonuç çıkar
    const sentences = abstract.split('.').filter(s => s.trim().length > 0);
    if (sentences.length > 0) {
      return sentences[sentences.length - 1].trim() + '.';
    }
  }
  
  return 'Sonuç bilgisi mevcut verilerle belirlenemedi. Tam metin analizi gerekli.';
}

function generateStatistics(journal, abstract) {
  if (abstract && abstract !== 'Özet mevcut değil') {
    if (abstract.includes('p<') || abstract.includes('P<')) {
      return 'İstatistiksel anlamlılık testleri uygulandı. P değerleri rapor edildi.';
    }
    if (abstract.includes('confidence interval') || abstract.includes('CI')) {
      return 'Güven aralıkları hesaplandı. İstatistiksel analiz yapıldı.';
    }
  }
  
  if (journal.includes('Clinical') || journal.includes('Medical')) {
    return 'Klinik araştırma standartlarına uygun istatistiksel yöntemler kullanıldı.';
  }
  
  return 'İstatistiksel yöntem bilgisi mevcut verilerle belirlenemedi.';
}

function generateLimitations(dataQuality, abstract) {
  const limitations = [];
  
  if (dataQuality === 'LIMITED') {
    limitations.push('Makale tam metnine erişim sağlanamadı');
    limitations.push('Analiz sadece mevcut metadata ile yapıldı');
  }
  
  if (abstract === 'Özet mevcut değil') {
    limitations.push('Makale özeti mevcut değil');
    limitations.push('Bulgular başlık ve metadata analizi ile tahmin edildi');
  }
  
  limitations.push('Detaylı analiz için orijinal makaleye başvurun');
  
  return limitations.join(', ');
}

async function enhancedTest() {
  try {
    console.log('🚀 Gelişmiş DOI analizi başlıyor...\n');
    
    const testDOI = '10.1007/s44163-022-00022-8';
    console.log(`📄 Test DOI: ${testDOI}\n`);
    
    // Paralel olarak tüm API'leri çağır
    console.log('📡 Birden fazla kaynaktan veri çekiliyor...');
    const [primaryResult, crossrefResult, semanticResult, pubmedResult] = await Promise.allSettled([
      lookupDOI(testDOI),
      fetchCrossrefData(testDOI),
      fetchSemanticScholarData(testDOI),
      fetchPubMedData(testDOI)
    ]);
    
    const primaryData = primaryResult.status === 'fulfilled' ? primaryResult.value : null;
    const crossrefData = crossrefResult.status === 'fulfilled' ? crossrefResult.value : null;
    const semanticData = semanticResult.status === 'fulfilled' ? semanticResult.value : null;
    const pubmedData = pubmedResult.status === 'fulfilled' ? pubmedResult.value : null;
    
    console.log('\n📊 Veri Kaynakları Durumu:');
    console.log(`Primary (uwdata): ${primaryData ? '✅' : '❌'}`);
    console.log(`Crossref: ${crossrefData ? '✅' : '❌'}`);
    console.log(`Semantic Scholar: ${semanticData ? '✅' : '❌'}`);
    console.log(`PubMed: ${pubmedData ? '✅' : '❌'}\n`);
    
    // Verileri analiz et
    const analysis = analyzeArticleData(primaryData, crossrefData, semanticData, pubmedData);
    
    // Sonuçları göster
    console.log('📋 MAKALE ANALİZİ SONUÇLARI');
    console.log('='.repeat(60));
    console.log(`📖 Başlık: ${analysis.title}`);
    console.log(`👥 Yazarlar: ${analysis.authors.slice(0, 100)}...`);
    console.log(`📚 Dergi: ${analysis.journal} (${analysis.year})`);
    console.log(`📊 Atıf Sayısı: ${analysis.citationCount}`);
    console.log(`🏷️ Anahtar Kelimeler: ${analysis.keywords || 'Mevcut değil'}`);
    console.log(`📝 Özet: ${analysis.abstract.slice(0, 200)}...`);
    console.log(`🔬 Veri Kalitesi: ${analysis.dataQuality}`);
    console.log(`⚠️ Sınırlamalar: ${analysis.limitations}\n`);
    
    console.log('🔍 BULGULAR:');
    console.log(analysis.findings);
    console.log('\n📋 METODOLOJİ:');
    console.log(analysis.methodology);
    console.log('\n📊 İSTATİSTİK:');
    console.log(analysis.statistics);
    console.log('\n🎯 SONUÇ:');
    console.log(analysis.conclusion);
    
    // Sonuçları dosyaya kaydet
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `enhanced-analysis-${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify({
      doi: testDOI,
      analysis,
      rawData: {
        primary: primaryData,
        crossref: crossrefData,
        semantic: semanticData,
        pubmed: pubmedData
      },
      processedAt: new Date().toISOString()
    }, null, 2));
    
    console.log(`\n✅ Analiz tamamlandı!`);
    console.log(`📁 Sonuçlar kaydedildi: ${filename}`);
    
    return analysis;
    
  } catch (error) {
    console.error('❌ Test hatası:', error);
    throw error;
  }
}

enhancedTest()
  .then(() => {
    console.log('\n🎉 Gelişmiş test başarıyla tamamlandı!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test başarısız:', error);
    process.exit(1);
  });
