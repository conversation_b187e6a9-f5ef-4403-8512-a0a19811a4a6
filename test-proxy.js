// API Proxy test
import fs from 'fs';

async function testProxy() {
  try {
    console.log('🧪 API Proxy test başlıyor...\n');
    
    const testDOI = '10.1007/s44163-022-00022-8';
    console.log(`📄 Test DOI: ${testDOI}`);
    
    // Health check
    console.log('🔍 Health check...');
    const healthResponse = await fetch('http://localhost:3001/api/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
    
    // DOI analizi
    console.log('\n📡 DOI analizi başlıyor...');
    const response = await fetch('http://localhost:3001/api/analyze-doi', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        doi: testDOI
      })
    });
    
    if (!response.ok) {
      throw new Error(`API Hatası: ${response.status}`);
    }
    
    const result = await response.json();
    
    console.log('\n📊 Sonuçlar:');
    console.log('='.repeat(50));
    console.log(`📖 Başlık: ${result.summary.title}`);
    console.log(`👥 Yazarlar: ${result.summary.authors.slice(0, 100)}...`);
    console.log(`📚 Dergi: ${result.summary.journal} (${result.summary.year})`);
    console.log(`🎯 Alan: ${result.summary.field}`);
    console.log(`🔬 Veri Kalitesi: ${result.summary.dataQuality}`);
    console.log(`📝 Özet: ${result.summary.abstract.slice(0, 100)}...`);
    
    // SVG'yi kaydet
    fs.writeFileSync('proxy-test-result.svg', result.svg);
    console.log('\n✅ SVG kaydedildi: proxy-test-result.svg');
    
    console.log('\n🎉 Proxy test başarılı!');
    console.log('🌐 Web sitesi: http://localhost:8000');
    
  } catch (error) {
    console.error('❌ Test hatası:', error);
  }
}

testProxy();
