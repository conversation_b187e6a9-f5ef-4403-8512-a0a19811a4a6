import { m as mastra } from './.mastra/output/mastra.mjs';
import fs from 'fs';

async function testDOISummary() {
  try {
    console.log('DOI özet sistemi test ediliyor...');

    // Test DOI - Transplant Glomerulopathy makalesi
    const testDOI = '10.1016/j.kint.2021.02.040';

    console.log(`Test DOI: ${testDOI}`);

    // Mastra'yı kontrol et
    console.log('Mastra:', mastra);
    console.log('Workflows:', mastra.workflows);

    // Workflow'u çalıştır
    const result = await mastra.workflows.doiSummaryWorkflow.execute({
      doi: testDOI
    });
    
    console.log('Workflow tamamlandı!');
    console.log('Makale Özeti:');
    console.log('='.repeat(50));
    console.log(`Başlık: ${result.summary.title}`);
    console.log(`Yazarlar: ${result.summary.authors}`);
    console.log(`Dergi: ${result.summary.journal} (${result.summary.year})`);
    console.log(`\nÖzet: ${result.summary.abstract.slice(0, 200)}...`);
    console.log(`\nAna Bulgular: ${result.summary.findings}`);
    console.log(`\nSonuç: ${result.summary.conclusion}`);
    console.log(`\nMetodoloji: ${result.summary.methodology}`);
    console.log(`\nİstatistik: ${result.summary.statistics}`);
    
    // SVG dosyasını kaydet
    fs.writeFileSync('article-summary.svg', result.svg);
    console.log('\nSVG infografik "article-summary.svg" dosyasına kaydedildi.');
    
    return result;
    
  } catch (error) {
    console.error('Test hatası:', error);
    throw error;
  }
}

// Test fonksiyonunu çalıştır
testDOISummary()
  .then(() => {
    console.log('\nTest başarıyla tamamlandı!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Test başarısız:', error);
    process.exit(1);
  });
