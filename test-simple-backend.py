#!/usr/bin/env python3

import urllib.request
import json
import time

def test_backend():
    print("🧪 Simple Backend test başlıyor...\n")
    
    # Health check
    print("🔍 Health check...")
    try:
        with urllib.request.urlopen('http://localhost:4567/health', timeout=5) as response:
            if response.status == 200:
                data = json.loads(response.read().decode())
                print("✅ Health check başarılı")
                print(f"   Status: {data['status']}")
                print(f"   Time: {data['timestamp']}")
            else:
                print(f"❌ Health check başarısız: {response.status}")
                return False
    except Exception as e:
        print(f"❌ Health check hatası: {e}")
        print("   Backend çalışıyor mu? (python simple-backend.py)")
        return False
    
    # DOI analizi
    print("\n📡 DOI analizi test ediliyor...")
    test_doi = '10.1007/s44163-022-00022-8'
    print(f"   Test DOI: {test_doi}")
    
    try:
        url = f"http://localhost:4567/summary?doi={urllib.parse.quote(test_doi)}"
        
        print("   İstek gönderiliyor...")
        start_time = time.time()
        
        with urllib.request.urlopen(url, timeout=60) as response:
            end_time = time.time()
            
            print(f"   Süre: {(end_time - start_time):.2f} saniye")
            print(f"   HTTP Status: {response.status}")
            
            if response.status == 200:
                result = json.loads(response.read().decode())
                
                print("\n📊 Sonuçlar:")
                print(f"   DOI: {result['doi']}")
                print(f"   Başlık: {result['analysis']['title']}")
                print(f"   Alan: {result['analysis']['field']}")
                print(f"   Veri Kalitesi: {result['analysis']['dataQuality']}")
                print(f"   PDF Erişimi: {'Evet' if result['pdf_access']['is_open_access'] else 'Hayır'}")
                
                if result['pdf_access']['is_open_access'] and result['pdf_access'].get('pdf_url'):
                    print(f"   PDF URL: {result['pdf_access']['pdf_url']}")
                
                print("\n✅ DOI analizi başarılı!")
                return True
            else:
                print(f"❌ DOI analizi başarısız: {response.status}")
                return False
        
    except Exception as e:
        print(f"❌ DOI analizi hatası: {e}")
        return False

def test_error_handling():
    print("\n🧪 Hata yönetimi test ediliyor...")
    
    # Geçersiz DOI
    try:
        with urllib.request.urlopen('http://localhost:4567/summary?doi=invalid-doi', timeout=10) as response:
            if response.status in [500, 400]:
                print(f"✅ Geçersiz DOI için uygun hata döndü: {response.status}")
            else:
                print(f"⚠️ Beklenmeyen response: {response.status}")
    except urllib.error.HTTPError as e:
        if e.code in [500, 400]:
            print(f"✅ Geçersiz DOI için uygun hata döndü: {e.code}")
        else:
            print(f"⚠️ Beklenmeyen HTTP error: {e.code}")
    except Exception as e:
        print(f"❌ Hata testi başarısız: {e}")
    
    # Eksik DOI parametresi
    try:
        with urllib.request.urlopen('http://localhost:4567/summary', timeout=10) as response:
            if response.status == 400:
                print(f"✅ Eksik DOI için uygun hata döndü: {response.status}")
            else:
                print(f"⚠️ Beklenmeyen response: {response.status}")
    except urllib.error.HTTPError as e:
        if e.code == 400:
            print(f"✅ Eksik DOI için uygun hata döndü: {e.code}")
        else:
            print(f"⚠️ Beklenmeyen HTTP error: {e.code}")
    except Exception as e:
        print(f"❌ Hata testi başarısız: {e}")

# Ana test
if __name__ == '__main__':
    print("🚀 Simple Python Backend Test Suite")
    print("=" * 50)
    
    success = test_backend()
    
    if success:
        test_error_handling()
        print("\n🎉 Tüm testler tamamlandı!")
        print("\n📝 Sonraki adımlar:")
        print("   1. Frontend'i başlat: cd frontend && npm run dev")
        print("   2. Tarayıcıda aç: http://localhost:3000")
        print("   3. DOI gir ve test et")
    else:
        print("\n💥 Test başarısız!")
        print("\n🔧 Sorun giderme:")
        print("   1. Backend çalışıyor mu? python simple-backend.py")
        print("   2. CLI agent çalışıyor mu? node cli-agent.js test-doi")
        print("   3. Node.js yüklü mü? node --version")
